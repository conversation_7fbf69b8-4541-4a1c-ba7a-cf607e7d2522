{"version": 3, "file": "AccessTokenEntity.js", "sources": ["../../../src/cache/entities/AccessTokenEntity.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { CredentialEntity } from \"./CredentialEntity\";\r\nimport { CredentialType, AuthenticationScheme } from \"../../utils/Constants\";\r\nimport { TimeUtils } from \"../../utils/TimeUtils\";\r\nimport { StringUtils } from \"../../utils/StringUtils\";\r\nimport { ICrypto } from \"../../crypto/ICrypto\";\r\nimport { TokenClaims } from \"../../account/TokenClaims\";\r\nimport { AuthToken } from \"../../account/AuthToken\";\r\nimport { ClientAuthError } from \"../../error/ClientAuthError\";\r\n\r\n/**\r\n * ACCESS_TOKEN Credential Type\r\n *\r\n * Key:Value Schema:\r\n *\r\n * Key Example: uid.utid-login.microsoftonline.com-accesstoken-clientId-contoso.com-user.read\r\n *\r\n * Value Schema:\r\n * {\r\n *      homeAccountId: home account identifier for the auth scheme,\r\n *      environment: entity that issued the token, represented as a full host\r\n *      credentialType: Type of credential as a string, can be one of the following: RefreshToken, AccessToken, IdToken, Password, Cookie, Certificate, Other\r\n *      clientId: client ID of the application\r\n *      secret: Actual credential as a string\r\n *      familyId: Family ID identifier, usually only used for refresh tokens\r\n *      realm: Full tenant or organizational identifier that the account belongs to\r\n *      target: Permissions that are included in the token, or for refresh tokens, the resource identifier.\r\n *      cachedAt: Absolute device time when entry was created in the cache.\r\n *      expiresOn: Token expiry time, calculated based on current UTC time in seconds. Represented as a string.\r\n *      extendedExpiresOn: Additional extended expiry time until when token is valid in case of server-side outage. Represented as string in UTC seconds.\r\n *      keyId: used for POP and SSH tokenTypes\r\n *      tokenType: Type of the token issued. Usually \"Bearer\"\r\n * }\r\n */\r\nexport class AccessTokenEntity extends CredentialEntity {\r\n    realm: string;\r\n    target: string;\r\n    cachedAt: string;\r\n    expiresOn: string;\r\n    extendedExpiresOn?: string;\r\n    refreshOn?: string;\r\n    keyId?: string; // for POP and SSH tokenTypes\r\n    tokenType?: AuthenticationScheme;\r\n    requestedClaims?: string;\r\n    requestedClaimsHash?: string;\r\n\r\n    /**\r\n     * Create AccessTokenEntity\r\n     * @param homeAccountId\r\n     * @param environment\r\n     * @param accessToken\r\n     * @param clientId\r\n     * @param tenantId\r\n     * @param scopes\r\n     * @param expiresOn\r\n     * @param extExpiresOn\r\n     */\r\n    static createAccessTokenEntity(\r\n        homeAccountId: string,\r\n        environment: string,\r\n        accessToken: string,\r\n        clientId: string,\r\n        tenantId: string,\r\n        scopes: string,\r\n        expiresOn: number,\r\n        extExpiresOn: number,\r\n        cryptoUtils: ICrypto,\r\n        refreshOn?: number,\r\n        tokenType?: AuthenticationScheme,\r\n        userAssertionHash?:string,\r\n        keyId?: string,\r\n        requestedClaims?: string,\r\n        requestedClaimsHash?: string\r\n    ): AccessTokenEntity {\r\n        const atEntity: AccessTokenEntity = new AccessTokenEntity();\r\n\r\n        atEntity.homeAccountId = homeAccountId;\r\n        atEntity.credentialType = CredentialType.ACCESS_TOKEN;\r\n        atEntity.secret = accessToken;\r\n\r\n        const currentTime = TimeUtils.nowSeconds();\r\n        atEntity.cachedAt = currentTime.toString();\r\n\r\n        /*\r\n         * Token expiry time.\r\n         * This value should be  calculated based on the current UTC time measured locally and the value  expires_in Represented as a string in JSON.\r\n         */\r\n        atEntity.expiresOn = expiresOn.toString();\r\n        atEntity.extendedExpiresOn = extExpiresOn.toString();\r\n        if (refreshOn) {\r\n            atEntity.refreshOn = refreshOn.toString();\r\n        }\r\n\r\n        atEntity.environment = environment;\r\n        atEntity.clientId = clientId;\r\n        atEntity.realm = tenantId;\r\n        atEntity.target = scopes;\r\n        atEntity.userAssertionHash = userAssertionHash;\r\n\r\n        atEntity.tokenType = StringUtils.isEmpty(tokenType) ? AuthenticationScheme.BEARER : tokenType;\r\n\r\n        if (requestedClaims) {\r\n            atEntity.requestedClaims = requestedClaims;\r\n            atEntity.requestedClaimsHash = requestedClaimsHash;\r\n        }\r\n\r\n        /*\r\n         * Create Access Token With Auth Scheme instead of regular access token\r\n         * Cast to lower to handle \"bearer\" from ADFS\r\n         */\r\n        if (atEntity.tokenType?.toLowerCase() !== AuthenticationScheme.BEARER.toLowerCase()) {\r\n            atEntity.credentialType = CredentialType.ACCESS_TOKEN_WITH_AUTH_SCHEME;\r\n            switch (atEntity.tokenType) {\r\n                case AuthenticationScheme.POP:\r\n                    // Make sure keyId is present and add it to credential\r\n                    const tokenClaims: TokenClaims | null = AuthToken.extractTokenClaims(accessToken, cryptoUtils);\r\n                    if (!tokenClaims?.cnf?.kid) {\r\n                        throw ClientAuthError.createTokenClaimsRequiredError();\r\n                    }\r\n                    atEntity.keyId = tokenClaims.cnf.kid;\r\n                    break;\r\n                case AuthenticationScheme.SSH:\r\n                    atEntity.keyId = keyId;\r\n            }\r\n        }\r\n\r\n        return atEntity;\r\n    }\r\n\r\n    /**\r\n     * Validates an entity: checks for all expected params\r\n     * @param entity\r\n     */\r\n    static isAccessTokenEntity(entity: object): boolean {\r\n\r\n        if (!entity) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            entity.hasOwnProperty(\"homeAccountId\") &&\r\n            entity.hasOwnProperty(\"environment\") &&\r\n            entity.hasOwnProperty(\"credentialType\") &&\r\n            entity.hasOwnProperty(\"realm\") &&\r\n            entity.hasOwnProperty(\"clientId\") &&\r\n            entity.hasOwnProperty(\"secret\") &&\r\n            entity.hasOwnProperty(\"target\") &&\r\n            (entity[\"credentialType\"] === CredentialType.ACCESS_TOKEN || entity[\"credentialType\"] === CredentialType.ACCESS_TOKEN_WITH_AUTH_SCHEME)\r\n        );\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AAWH;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACH,IAAA,iBAAA,kBAAA,UAAA,MAAA,EAAA;IAAuC,SAAgB,CAAA,iBAAA,EAAA,MAAA,CAAA,CAAA;AAAvD,IAAA,SAAA,iBAAA,GAAA;;KAoHC;AAxGG;;;;;;;;;;AAUG;AACI,IAAA,iBAAA,CAAA,uBAAuB,GAA9B,UACI,aAAqB,EACrB,WAAmB,EACnB,WAAmB,EACnB,QAAgB,EAChB,QAAgB,EAChB,MAAc,EACd,SAAiB,EACjB,YAAoB,EACpB,WAAoB,EACpB,SAAkB,EAClB,SAAgC,EAChC,iBAAyB,EACzB,KAAc,EACd,eAAwB,EACxB,mBAA4B,EAAA;;AAE5B,QAAA,IAAM,QAAQ,GAAsB,IAAI,iBAAiB,EAAE,CAAC;AAE5D,QAAA,QAAQ,CAAC,aAAa,GAAG,aAAa,CAAC;AACvC,QAAA,QAAQ,CAAC,cAAc,GAAG,cAAc,CAAC,YAAY,CAAC;AACtD,QAAA,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC;AAE9B,QAAA,IAAM,WAAW,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AAC3C,QAAA,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC;AAE3C;;;AAGG;AACH,QAAA,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC1C,QAAA,QAAQ,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;AACrD,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC7C,SAAA;AAED,QAAA,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,QAAA,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,QAAA,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC;AAC1B,QAAA,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,QAAA,QAAQ,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAE/C,QAAA,QAAQ,CAAC,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,oBAAoB,CAAC,MAAM,GAAG,SAAS,CAAC;AAE9F,QAAA,IAAI,eAAe,EAAE;AACjB,YAAA,QAAQ,CAAC,eAAe,GAAG,eAAe,CAAC;AAC3C,YAAA,QAAQ,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AACtD,SAAA;AAED;;;AAGG;AACH,QAAA,IAAI,CAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,SAAS,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,EAAO,MAAA,oBAAoB,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE;AACjF,YAAA,QAAQ,CAAC,cAAc,GAAG,cAAc,CAAC,6BAA6B,CAAC;YACvE,QAAQ,QAAQ,CAAC,SAAS;gBACtB,KAAK,oBAAoB,CAAC,GAAG;;oBAEzB,IAAM,WAAW,GAAuB,SAAS,CAAC,kBAAkB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;oBAC/F,IAAI,EAAA,CAAA,EAAA,GAAC,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,GAAG,CAAA,EAAE;AACxB,wBAAA,MAAM,eAAe,CAAC,8BAA8B,EAAE,CAAC;AAC1D,qBAAA;oBACD,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC;oBACrC,MAAM;gBACV,KAAK,oBAAoB,CAAC,GAAG;AACzB,oBAAA,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC9B,aAAA;AACJ,SAAA;AAED,QAAA,OAAO,QAAQ,CAAC;KACnB,CAAA;AAED;;;AAGG;IACI,iBAAmB,CAAA,mBAAA,GAA1B,UAA2B,MAAc,EAAA;QAErC,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,QACI,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC;AACtC,YAAA,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC;AACpC,YAAA,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACvC,YAAA,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;AAC9B,YAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AACjC,YAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC/B,YAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC/B,aAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,cAAc,CAAC,YAAY,IAAI,MAAM,CAAC,gBAAgB,CAAC,KAAK,cAAc,CAAC,6BAA6B,CAAC,EACzI;KACL,CAAA;IACL,OAAC,iBAAA,CAAA;AAAD,CApHA,CAAuC,gBAAgB,CAoHtD;;;;"}