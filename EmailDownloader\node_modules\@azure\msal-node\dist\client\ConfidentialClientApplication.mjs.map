{"version": 3, "file": "ConfidentialClientApplication.mjs", "sources": ["../../src/client/ConfidentialClientApplication.ts"], "sourcesContent": [null], "names": ["NodeConstants"], "mappings": ";;;;;;;;;AAAA;;;AAGG;AAEH;AAiCA;;;;AAIG;AACG,MAAO,6BACT,SAAQ,iBAAiB,CAAA;AAKzB;;;;;;;;;;;;;;;;;;AAkBG;AACH,IAAA,WAAA,CAAY,aAA4B,EAAA;QACpC,KAAK,CAAC,aAAa,CAAC,CAAC;QAErB,MAAM,oBAAoB,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;QAC7D,MAAM,uBAAuB,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;AACnE,QAAA,MAAM,mBAAmB,GACrB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,UAAU;YAC7C,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,gBAAgB;YAC1D,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC;AAErD;;;AAGG;QACH,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO;AACV,SAAA;;AAGD,QAAA,IACI,CAAC,oBAAoB,IAAI,uBAAuB;aAC/C,uBAAuB,IAAI,mBAAmB,CAAC;AAChD,aAAC,oBAAoB,IAAI,mBAAmB,CAAC,EAC/C;AACE,YAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,uBAAuB,CAC/C,CAAC;AACL,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE;YAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;YAClD,OAAO;AACV,SAAA;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;AAClC,YAAA,IAAI,CAAC,gCAAgC;AACjC,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;YACrC,OAAO;AACV,SAAA;QAED,IAAI,CAAC,mBAAmB,EAAE;AACtB,YAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,uBAAuB,CAC/C,CAAC;AACL,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB;iBACtD,gBAAgB;AACjB,kBAAE,eAAe,CAAC,mCAAmC,CAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CACzC;kBACD,eAAe,CAAC,eAAe;;AAE3B,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAoB,EACvD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CACzC,CAAC;AACX,SAAA;AACD,QAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;KACrC;AAED;;;;;AAKG;AACH,IAAA,mBAAmB,CAAC,QAA2B,EAAA;AAC3C,QAAA,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;KACpC;AAED;;AAEG;IACI,MAAM,8BAA8B,CACvC,OAAgC,EAAA;QAEhC,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,uCAAuC,EACvC,OAAO,CAAC,aAAa,CACxB,CAAC;;AAGF,QAAA,IAAI,eAAgD,CAAC;QACrD,IAAI,OAAO,CAAC,eAAe,EAAE;AACzB,YAAA,eAAe,GAAG;AACd,gBAAA,SAAS,EAAE,MAAM,kBAAkB,CAC/B,OAAO,CAAC,eAAe,EACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;;AAE5B,iBAAA;gBACD,aAAa,EAAEA,SAAa,CAAC,yBAAyB;aACzD,CAAC;AACL,SAAA;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;;AAG9D,QAAA,MAAM,gBAAgB,GAAG;AACrB,YAAA,GAAG,WAAW;AACd,YAAA,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM,CAC7B,CAAC,KAAa,KAAK,CAAC,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAC1D;SACJ,CAAC;AAEF,QAAA,MAAM,YAAY,GAAkC;AAChD,YAAA,GAAG,OAAO;AACV,YAAA,GAAG,gBAAgB;YACnB,eAAe;SAClB,CAAC;AAEF;;;AAGG;QACH,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAG,SAAS,CAAC,gBAAgB,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC9D,IACI,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CACzC,QAAiC,CACpC,EACH;AACE,YAAA,MAAM,qBAAqB,CACvB,oBAAoB,CAAC,oBAAoB,CAC5C,CAAC;AACL,SAAA;AAED;;;AAGG;QACH,MAAM,qBAAqB,GACvB,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AAEnC,QAAA,IAAI,MAA+B,CAAC;AACpC,QAAA,IAAI,YAAY,CAAC,WAAW,KAAK,wBAAwB,EAAE;AACvD,YAAA,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,qBAAqB,EAAE;gBACpD,MAAM,GAAG,qBAAqB,CAAC;AAClC,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC;AACrC,aAAA;AACJ,SAAA;AAED,QAAA,MAAM,wBAAwB,GAA6B;AACvD,YAAA,WAAW,EAAE,MAAM;AACnB,YAAA,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;SAC9D,CAAC;AAEF,QAAA,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,8BAA8B,EACpC,YAAY,CAAC,aAAa,EAC1B,YAAY,CAAC,SAAS,CACzB,CAAC;QACF,IAAI;YACA,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,eAAe,CAClD,YAAY,CAAC,SAAS,EACtB,YAAY,CAAC,aAAa,EAC1B,wBAAwB,EACxB,OAAO,CAAC,iBAAiB,CAC5B,CAAC;AACF,YAAA,MAAM,sBAAsB,GACxB,MAAM,IAAI,CAAC,6BAA6B,CACpC,mBAAmB,EACnB,YAAY,CAAC,aAAa,EAC1B,EAAE,EACF,sBAAsB,CACzB,CAAC;YACN,MAAM,sBAAsB,GAAG,IAAI,sBAAsB,CACrD,sBAAsB,EACtB,IAAI,CAAC,gBAAgB,CACxB,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,kCAAkC,EAClC,YAAY,CAAC,aAAa,CAC7B,CAAC;AACF,YAAA,OAAO,MAAM,sBAAsB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAClE,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACxB,gBAAA,CAAC,CAAC,gBAAgB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AAClD,aAAA;AACD,YAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAC7C,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;;;;;;;;;AAUG;IACI,MAAM,sBAAsB,CAC/B,OAA0B,EAAA;QAE1B,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,+BAA+B,EAC/B,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,QAAA,MAAM,YAAY,GAA4B;AAC1C,YAAA,GAAG,OAAO;YACV,IAAI,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;SACjD,CAAC;QACF,IAAI;YACA,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,eAAe,CAClD,YAAY,CAAC,SAAS,EACtB,YAAY,CAAC,aAAa,EAC1B,SAAS,EACT,OAAO,CAAC,iBAAiB,CAC5B,CAAC;AACF,YAAA,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAC7D,mBAAmB,EACnB,YAAY,CAAC,aAAa,EAC1B,EAAE,EACF,SAAS,CACZ,CAAC;AACF,YAAA,MAAM,SAAS,GAAG,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,6BAA6B,EAC7B,YAAY,CAAC,aAAa,CAC7B,CAAC;AACF,YAAA,OAAO,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AACrD,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACxB,gBAAA,CAAC,CAAC,gBAAgB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AAClD,aAAA;AACD,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AACJ;;;;"}