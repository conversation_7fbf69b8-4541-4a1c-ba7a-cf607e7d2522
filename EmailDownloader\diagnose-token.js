// Diagnostic script to analyze the access token and understand permission issues
require('dotenv').config();
const msal = require('@azure/msal-node');

function decodeJWT(token) {
    try {
        // JWT has 3 parts separated by dots
        const parts = token.split('.');
        if (parts.length !== 3) {
            throw new Error('Invalid JWT format');
        }
        
        // Decode the payload (second part)
        const payload = parts[1];
        // Add padding if needed
        const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
        const decodedPayload = Buffer.from(paddedPayload, 'base64').toString('utf8');
        
        return JSON.parse(decodedPayload);
    } catch (error) {
        console.error('Error decoding JWT:', error.message);
        return null;
    }
}

async function diagnoseToken() {
    console.log("=== ACCESS TOKEN DIAGNOSTIC ===\n");
    
    const msalConfig = {
        auth: {
            clientId: process.env.CLIENT_ID,
            authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
            clientSecret: process.env.CLIENT_SECRET
        }
    };
    
    try {
        console.log("1. Acquiring access token...");
        const cca = new msal.ConfidentialClientApplication(msalConfig);
        const tokenRequest = { scopes: ['https://graph.microsoft.com/.default'] };
        const response = await cca.acquireTokenByClientCredential(tokenRequest);
        
        console.log("✅ Token acquired successfully\n");
        
        // Decode and analyze the token
        console.log("2. Analyzing token contents...");
        const tokenPayload = decodeJWT(response.accessToken);
        
        if (tokenPayload) {
            console.log("Token Information:");
            console.log(`  App ID: ${tokenPayload.appid || 'Not found'}`);
            console.log(`  Tenant ID: ${tokenPayload.tid || 'Not found'}`);
            console.log(`  Audience: ${tokenPayload.aud || 'Not found'}`);
            console.log(`  Issuer: ${tokenPayload.iss || 'Not found'}`);
            console.log(`  Token Type: ${tokenPayload.app_displayname ? 'Application' : 'Delegated'}`);
            
            if (tokenPayload.exp) {
                const expDate = new Date(tokenPayload.exp * 1000);
                console.log(`  Expires: ${expDate.toISOString()}`);
            }
            
            console.log("\n3. Checking permissions (roles/scopes):");
            
            // Check for application permissions (roles)
            if (tokenPayload.roles && tokenPayload.roles.length > 0) {
                console.log("  Application Permissions (roles):");
                tokenPayload.roles.forEach(role => {
                    console.log(`    ✅ ${role}`);
                });
            } else {
                console.log("  ❌ No application permissions (roles) found in token");
            }
            
            // Check for delegated permissions (scopes)
            if (tokenPayload.scp) {
                console.log("  Delegated Permissions (scopes):");
                const scopes = tokenPayload.scp.split(' ');
                scopes.forEach(scope => {
                    console.log(`    - ${scope}`);
                });
            }
            
            console.log("\n4. Required permissions for your app:");
            console.log("  ✅ Mail.Read - Read mail in all mailboxes");
            console.log("  ✅ User.Read.All - Read all users' profiles");
            console.log("  ⚠️  Mail.ReadWrite - Read and write mail (optional)");
            
            console.log("\n5. Diagnosis:");
            const hasMailRead = tokenPayload.roles && tokenPayload.roles.includes('Mail.Read');
            const hasUserRead = tokenPayload.roles && tokenPayload.roles.includes('User.Read.All');
            
            if (!hasMailRead) {
                console.log("  ❌ Missing Mail.Read permission");
            }
            if (!hasUserRead) {
                console.log("  ❌ Missing User.Read.All permission");
            }
            
            if (!hasMailRead || !hasUserRead) {
                console.log("\n🔧 TO FIX THIS ISSUE:");
                console.log("1. Go to Azure Portal: https://portal.azure.com");
                console.log("2. Navigate to: Azure Active Directory > App registrations");
                console.log(`3. Find your app: ${process.env.CLIENT_ID}`);
                console.log("4. Click 'API permissions' in the left menu");
                console.log("5. Click 'Add a permission' > Microsoft Graph > Application permissions");
                console.log("6. Add these permissions:");
                if (!hasMailRead) console.log("   - Mail.Read");
                if (!hasUserRead) console.log("   - User.Read.All");
                console.log("7. ⚠️  IMPORTANT: Click 'Grant admin consent for [organization]'");
                console.log("8. Wait 5-10 minutes for changes to take effect");
            } else {
                console.log("  ✅ All required permissions are present!");
                console.log("  The issue might be elsewhere (mailbox access, conditional access, etc.)");
            }
            
        } else {
            console.log("❌ Could not decode token payload");
        }
        
    } catch (error) {
        console.error("❌ Error:", error.message);
    }
}

diagnoseToken().catch(console.error);
