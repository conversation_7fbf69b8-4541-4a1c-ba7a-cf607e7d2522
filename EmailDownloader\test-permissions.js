// Simple test script to verify Microsoft Graph permissions
require('dotenv').config();
const msal = require('@azure/msal-node');
const axios = require('axios');

async function testPermissions() {
    console.log("Testing Microsoft Graph API permissions...\n");
    
    // Create MSAL configuration
    const msalConfig = {
        auth: {
            clientId: process.env.CLIENT_ID,
            authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
            clientSecret: process.env.CLIENT_SECRET
        }
    };
    
    try {
        // Get access token
        console.log("1. Getting access token...");
        const cca = new msal.ConfidentialClientApplication(msalConfig);
        const tokenRequest = { scopes: ['https://graph.microsoft.com/.default'] };
        const response = await cca.acquireTokenByClientCredential(tokenRequest);
        console.log("✅ Access token acquired successfully\n");
        
        // Create API client
        const api = axios.create({
            baseURL: 'https://graph.microsoft.com/v1.0',
            headers: { Authorization: `Bearer ${response.accessToken}` }
        });
        
        // Test 1: Get user info
        console.log("2. Testing user access...");
        try {
            const userResponse = await api.get(`/users/${process.env.MAIL_USER_ID}`);
            console.log(`✅ User found: ${userResponse.data.displayName} (${userResponse.data.mail})\n`);
        } catch (error) {
            console.log(`❌ User access failed: ${error.response?.data?.error?.message || error.message}\n`);
        }
        
        // Test 2: List mail folders
        console.log("3. Testing mail folder access...");
        try {
            const foldersResponse = await api.get(`/users/${process.env.MAIL_USER_ID}/mailFolders`);
            console.log(`✅ Found ${foldersResponse.data.value.length} mail folders:`);
            foldersResponse.data.value.slice(0, 5).forEach(folder => {
                console.log(`   - ${folder.displayName} (${folder.totalItemCount} items)`);
            });
            
            // Check for target folder
            const targetFolder = foldersResponse.data.value.find(f => 
                f.displayName.toLowerCase() === process.env.TARGET_FOLDER_NAME.toLowerCase()
            );
            if (targetFolder) {
                console.log(`✅ Target folder '${process.env.TARGET_FOLDER_NAME}' found with ${targetFolder.totalItemCount} items\n`);
            } else {
                console.log(`❌ Target folder '${process.env.TARGET_FOLDER_NAME}' not found\n`);
            }
            
        } catch (error) {
            console.log(`❌ Mail folder access failed: ${error.response?.data?.error?.message || error.message}\n`);
        }
        
        // Test 3: Try to get messages from inbox
        console.log("4. Testing message access...");
        try {
            const messagesResponse = await api.get(`/users/${process.env.MAIL_USER_ID}/messages?$top=1`);
            console.log(`✅ Message access successful. Found ${messagesResponse.data.value.length} messages\n`);
        } catch (error) {
            console.log(`❌ Message access failed: ${error.response?.data?.error?.message || error.message}\n`);
        }
        
        console.log("Permission test completed!");
        
    } catch (error) {
        console.error("❌ Authentication failed:", error.message);
        console.error("Check your CLIENT_ID, CLIENT_SECRET, and TENANT_ID in the .env file");
    }
}

// Run the test
testPermissions().catch(console.error);
