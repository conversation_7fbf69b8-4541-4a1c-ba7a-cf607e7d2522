import { CommonClientCredentialRequest } from "@azure/msal-common";
/**
 * CommonClientCredentialRequest
 * - scopes                  - Array of scopes the application is requesting access to.
 * - authority               - URL of the authority, the security token service (STS) from which MSAL will acquire tokens.
 * - correlationId           - Unique GUID set per request to trace a request end-to-end for telemetry purposes.
 * - skipCache               - Skip token cache lookup and force request to authority to get a a new token. Defaults to false.
 * - clientAssertion         - A Base64Url-encoded signed JWT assertion string used in the Client Credential flow
 * - tokenQueryParameters    - String to string map of custom query parameters added to the /token call
 * @public
 */
export declare type ClientCredentialRequest = Partial<Omit<CommonClientCredentialRequest, "scopes" | "resourceRequestMethod" | "resourceRequestUri" | "requestedClaimsHash" | "clientAssertion">> & {
    scopes: Array<string>;
    clientAssertion?: string;
};
//# sourceMappingURL=ClientCredentialRequest.d.ts.map