{"version": 3, "file": "ClientConfigurationError.mjs", "sources": ["../../src/error/ClientConfigurationError.ts"], "sourcesContent": [null], "names": ["ClientConfigurationErrorCodes.redirectUriEmpty", "ClientConfigurationErrorCodes.claimsRequestParsingError", "ClientConfigurationErrorCodes.authorityUriInsecure", "ClientConfigurationErrorCodes.urlParseError", "ClientConfigurationErrorCodes.urlEmptyError", "ClientConfigurationErrorCodes.emptyInputScopesError", "ClientConfigurationErrorCodes.invalidClaims", "ClientConfigurationErrorCodes.tokenRequestEmpty", "ClientConfigurationErrorCodes.logoutRequestEmpty", "ClientConfigurationErrorCodes.invalidCodeChallengeMethod", "ClientConfigurationErrorCodes.pkceParamsMissing", "ClientConfigurationErrorCodes.invalidCloudDiscoveryMetadata", "ClientConfigurationErrorCodes.invalidAuthorityMetadata", "ClientConfigurationErrorCodes.untrustedAuthority", "ClientConfigurationErrorCodes.missingSshJwk", "ClientConfigurationErrorCodes.missingSshKid", "ClientConfigurationErrorCodes.missingNonceAuthenticationHeader", "ClientConfigurationErrorCodes.invalidAuthenticationHeader", "ClientConfigurationErrorCodes.cannotSetOIDCOptions", "ClientConfigurationErrorCodes.cannotAllowPlatformBroker", "ClientConfigurationErrorCodes.authorityMismatch", "ClientConfigurationErrorCodes.invalidAuthorizePostBodyParameters", "ClientConfigurationErrorCodes.invalidRequestMethodForEAR"], "mappings": ";;;;;;;AAAA;;;AAGG;AAMU,MAAA,gCAAgC,GAAG;AAC5C,IAAA,CAACA,gBAA8C,GAC3C,kEAAkE;AACtE,IAAA,CAACC,yBAAuD,GACpD,kDAAkD;AACtD,IAAA,CAACC,oBAAkD,GAC/C,2NAA2N;AAC/N,IAAA,CAACC,aAA2C,GACxC,oDAAoD;AACxD,IAAA,CAACC,aAA2C,GAAG,wBAAwB;AACvE,IAAA,CAACC,qBAAmD,GAChD,gHAAgH;AACpH,IAAA,CAACC,aAA2C,GACxC,2DAA2D;AAC/D,IAAA,CAACC,iBAA+C,GAC5C,iDAAiD;AACrD,IAAA,CAACC,kBAAgD,GAC7C,2CAA2C;AAC/C,IAAA,CAACC,0BAAwD,GACrD,+EAA+E;AACnF,IAAA,CAACC,iBAA+C,GAC5C,qGAAqG;AACzG,IAAA,CAACC,6BAA2D,GACxD,qIAAqI;AACzI,IAAA,CAACC,wBAAsD,GACnD,yIAAyI;AAC7I,IAAA,CAACC,kBAAgD,GAC7C,4HAA4H;AAChI,IAAA,CAACC,aAA2C,GACxC,6HAA6H;AACjI,IAAA,CAACC,aAA2C,GACxC,uJAAuJ;AAC3J,IAAA,CAACC,gCAA8D,GAC3D,gLAAgL;AACpL,IAAA,CAACC,2BAAyD,GACtD,wCAAwC;AAC5C,IAAA,CAACC,oBAAkD,GAC/C,6GAA6G;AACjH,IAAA,CAACC,yBAAuD,GACpD,iFAAiF;AACrF,IAAA,CAACC,iBAA+C,GAC5C,kPAAkP;AACtP,IAAA,CAACC,kCAAgE,GAC7D,iLAAiL;AACrL,IAAA,CAACC,0BAAwD,GACrD,wJAAwJ;EAC9J;AAEF;;;AAGG;AACU,MAAA,+BAA+B,GAAG;AAC3C,IAAA,iBAAiB,EAAE;QACf,IAAI,EAAEtB,gBAA8C;AACpD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,gBAA8C,CACjD;AACJ,KAAA;AACD,IAAA,yBAAyB,EAAE;QACvB,IAAI,EAAEC,yBAAuD;AAC7D,QAAA,IAAI,EAAE,gCAAgC,CAClCA,yBAAuD,CAC1D;AACJ,KAAA;AACD,IAAA,oBAAoB,EAAE;QAClB,IAAI,EAAEC,oBAAkD;AACxD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,oBAAkD,CACrD;AACJ,KAAA;AACD,IAAA,aAAa,EAAE;QACX,IAAI,EAAEC,aAA2C;AACjD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,aAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,aAAa,EAAE;QACX,IAAI,EAAEC,aAA2C;AACjD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,aAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,gBAAgB,EAAE;QACd,IAAI,EAAEC,qBAAmD;AACzD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,qBAAmD,CACtD;AACJ,KAAA;AACD,IAAA,oBAAoB,EAAE;QAClB,IAAI,EAAEC,aAA2C;AACjD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,aAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,sBAAsB,EAAE;QACpB,IAAI,EAAEC,iBAA+C;AACrD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,iBAA+C,CAClD;AACJ,KAAA;AACD,IAAA,uBAAuB,EAAE;QACrB,IAAI,EAAEC,kBAAgD;AACtD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,kBAAgD,CACnD;AACJ,KAAA;AACD,IAAA,0BAA0B,EAAE;QACxB,IAAI,EAAEC,0BAAwD;AAC9D,QAAA,IAAI,EAAE,gCAAgC,CAClCA,0BAAwD,CAC3D;AACJ,KAAA;AACD,IAAA,0BAA0B,EAAE;QACxB,IAAI,EAAEC,iBAA+C;AACrD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,iBAA+C,CAClD;AACJ,KAAA;AACD,IAAA,6BAA6B,EAAE;QAC3B,IAAI,EAAEC,6BAA2D;AACjE,QAAA,IAAI,EAAE,gCAAgC,CAClCA,6BAA2D,CAC9D;AACJ,KAAA;AACD,IAAA,wBAAwB,EAAE;QACtB,IAAI,EAAEC,wBAAsD;AAC5D,QAAA,IAAI,EAAE,gCAAgC,CAClCA,wBAAsD,CACzD;AACJ,KAAA;AACD,IAAA,kBAAkB,EAAE;QAChB,IAAI,EAAEC,kBAAgD;AACtD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,kBAAgD,CACnD;AACJ,KAAA;AACD,IAAA,aAAa,EAAE;QACX,IAAI,EAAEC,aAA2C;AACjD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,aAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,aAAa,EAAE;QACX,IAAI,EAAEC,aAA2C;AACjD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,aAA2C,CAC9C;AACJ,KAAA;AACD,IAAA,gCAAgC,EAAE;QAC9B,IAAI,EAAEC,gCAA8D;AACpE,QAAA,IAAI,EAAE,gCAAgC,CAClCA,gCAA8D,CACjE;AACJ,KAAA;AACD,IAAA,2BAA2B,EAAE;QACzB,IAAI,EAAEC,2BAAyD;AAC/D,QAAA,IAAI,EAAE,gCAAgC,CAClCA,2BAAyD,CAC5D;AACJ,KAAA;AACD,IAAA,oBAAoB,EAAE;QAClB,IAAI,EAAEC,oBAAkD;AACxD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,oBAAkD,CACrD;AACJ,KAAA;AACD,IAAA,yBAAyB,EAAE;QACvB,IAAI,EAAEC,yBAAuD;AAC7D,QAAA,IAAI,EAAE,gCAAgC,CAClCA,yBAAuD,CAC1D;AACJ,KAAA;AACD,IAAA,iBAAiB,EAAE;QACf,IAAI,EAAEC,iBAA+C;AACrD,QAAA,IAAI,EAAE,gCAAgC,CAClCA,iBAA+C,CAClD;AACJ,KAAA;AACD,IAAA,kCAAkC,EAAE;QAChC,IAAI,EAAEC,kCAAgE;AACtE,QAAA,IAAI,EAAE,gCAAgC,CAClCA,kCAAgE,CACnE;AACJ,KAAA;AACD,IAAA,0BAA0B,EAAE;QACxB,IAAI,EAAEC,0BAAwD;AAC9D,QAAA,IAAI,EAAE,gCAAgC,CAClCA,0BAAwD,CAC3D;AACJ,KAAA;EACH;AAEF;;AAEG;AACG,MAAO,wBAAyB,SAAQ,SAAS,CAAA;AACnD,IAAA,WAAA,CAAY,SAAiB,EAAA;QACzB,KAAK,CAAC,SAAS,EAAE,gCAAgC,CAAC,SAAS,CAAC,CAAC,CAAC;AAC9D,QAAA,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;QACvC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,wBAAwB,CAAC,SAAS,CAAC,CAAC;KACnE;AACJ,CAAA;AAEK,SAAU,8BAA8B,CAC1C,SAAiB,EAAA;AAEjB,IAAA,OAAO,IAAI,wBAAwB,CAAC,SAAS,CAAC,CAAC;AACnD;;;;"}