{"version": 3, "file": "JoseHeaderError.js", "sources": ["../../src/error/JoseHeaderError.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { AuthError } from \"./AuthError\";\r\n\r\n/**\r\n * ClientAuthErrorMessage class containing string constants used by error codes and messages.\r\n */\r\nexport const JoseHeaderErrorMessage = {\r\n    missingKidError: {\r\n        code: \"missing_kid_error\",\r\n        desc: \"The JOSE Header for the requested JWT, JWS or JWK object requires a keyId to be configured as the 'kid' header claim. No 'kid' value was provided.\"\r\n    },\r\n    missingAlgError: {\r\n        code: \"missing_alg_error\",\r\n        desc: \"The JOSE Header for the requested JWT, JWS or JWK object requires an algorithm to be specified as the 'alg' header claim. No 'alg' value was provided.\"\r\n    },\r\n};\r\n\r\n/**\r\n * Error thrown when there is an error in the client code running on the browser.\r\n */\r\nexport class JoseHeaderError extends AuthError {\r\n    constructor(errorCode: string, errorMessage?: string) {\r\n        super(errorCode, errorMessage);\r\n        this.name = \"JoseHeaderError\";\r\n\r\n        Object.setPrototypeOf(this, JoseHeaderError.prototype);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when keyId isn't set on JOSE header.\r\n     */\r\n    static createMissingKidError(): JoseHeaderError {\r\n        return new JoseHeaderError(JoseHeaderErrorMessage.missingKidError.code, JoseHeaderErrorMessage.missingKidError.desc);\r\n    }\r\n\r\n    /**\r\n     * Creates an error thrown when algorithm isn't set on JOSE header.\r\n     */\r\n    static createMissingAlgError(): JoseHeaderError {\r\n        return new JoseHeaderError(JoseHeaderErrorMessage.missingAlgError.code, JoseHeaderErrorMessage.missingAlgError.desc);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAIH;;AAEG;AACU,IAAA,sBAAsB,GAAG;AAClC,IAAA,eAAe,EAAE;AACb,QAAA,IAAI,EAAE,mBAAmB;AACzB,QAAA,IAAI,EAAE,oJAAoJ;AAC7J,KAAA;AACD,IAAA,eAAe,EAAE;AACb,QAAA,IAAI,EAAE,mBAAmB;AACzB,QAAA,IAAI,EAAE,wJAAwJ;AACjK,KAAA;EACH;AAEF;;AAEG;AACH,IAAA,eAAA,kBAAA,UAAA,MAAA,EAAA;IAAqC,SAAS,CAAA,eAAA,EAAA,MAAA,CAAA,CAAA;IAC1C,SAAY,eAAA,CAAA,SAAiB,EAAE,YAAqB,EAAA;AAApD,QAAA,IAAA,KAAA,GACI,MAAM,CAAA,IAAA,CAAA,IAAA,EAAA,SAAS,EAAE,YAAY,CAAC,IAIjC,IAAA,CAAA;AAHG,QAAA,KAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAE9B,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC;;KAC1D;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,qBAAqB,GAA5B,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,EAAE,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;KACxH,CAAA;AAED;;AAEG;AACI,IAAA,eAAA,CAAA,qBAAqB,GAA5B,YAAA;AACI,QAAA,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,eAAe,CAAC,IAAI,EAAE,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;KACxH,CAAA;IACL,OAAC,eAAA,CAAA;AAAD,CArBA,CAAqC,SAAS,CAqB7C;;;;"}