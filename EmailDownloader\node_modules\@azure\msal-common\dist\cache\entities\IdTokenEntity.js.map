{"version": 3, "file": "IdTokenEntity.js", "sources": ["../../../src/cache/entities/IdTokenEntity.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { CredentialEntity } from \"./CredentialEntity\";\r\nimport { CredentialType } from \"../../utils/Constants\";\r\n\r\n/**\r\n * ID_TOKEN Cache\r\n *\r\n * Key:Value Schema:\r\n *\r\n * Key Example: uid.utid-login.microsoftonline.com-idtoken-clientId-contoso.com-\r\n *\r\n * Value Schema:\r\n * {\r\n *      homeAccountId: home account identifier for the auth scheme,\r\n *      environment: entity that issued the token, represented as a full host\r\n *      credentialType: Type of credential as a string, can be one of the following: RefreshToken, AccessToken, IdToken, Password, Cookie, Certificate, Other\r\n *      clientId: client ID of the application\r\n *      secret: Actual credential as a string\r\n *      realm: Full tenant or organizational identifier that the account belongs to\r\n * }\r\n */\r\nexport class IdTokenEntity extends CredentialEntity {\r\n    realm: string;\r\n\r\n    /**\r\n     * Create IdTokenEntity\r\n     * @param homeAccountId\r\n     * @param authenticationResult\r\n     * @param clientId\r\n     * @param authority\r\n     */\r\n    static createIdTokenEntity(\r\n        homeAccountId: string,\r\n        environment: string,\r\n        idToken: string,\r\n        clientId: string,\r\n        tenantId: string,\r\n    ): IdTokenEntity {\r\n        const idTokenEntity = new IdTokenEntity();\r\n\r\n        idTokenEntity.credentialType = CredentialType.ID_TOKEN;\r\n        idTokenEntity.homeAccountId = homeAccountId;\r\n        idTokenEntity.environment = environment;\r\n        idTokenEntity.clientId = clientId;\r\n        idTokenEntity.secret = idToken;\r\n        idTokenEntity.realm = tenantId;\r\n\r\n        return idTokenEntity;\r\n    }\r\n\r\n    /**\r\n     * Validates an entity: checks for all expected params\r\n     * @param entity\r\n     */\r\n    static isIdTokenEntity(entity: object): boolean {\r\n\r\n        if (!entity) {\r\n            return false;\r\n        }\r\n\r\n        return (\r\n            entity.hasOwnProperty(\"homeAccountId\") &&\r\n            entity.hasOwnProperty(\"environment\") &&\r\n            entity.hasOwnProperty(\"credentialType\") &&\r\n            entity.hasOwnProperty(\"realm\") &&\r\n            entity.hasOwnProperty(\"clientId\") &&\r\n            entity.hasOwnProperty(\"secret\") &&\r\n            entity[\"credentialType\"] === CredentialType.ID_TOKEN\r\n        );\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAKH;;;;;;;;;;;;;;;;AAgBG;AACH,IAAA,aAAA,kBAAA,UAAA,MAAA,EAAA;IAAmC,SAAgB,CAAA,aAAA,EAAA,MAAA,CAAA,CAAA;AAAnD,IAAA,SAAA,aAAA,GAAA;;KAiDC;AA9CG;;;;;;AAMG;IACI,aAAmB,CAAA,mBAAA,GAA1B,UACI,aAAqB,EACrB,WAAmB,EACnB,OAAe,EACf,QAAgB,EAChB,QAAgB,EAAA;AAEhB,QAAA,IAAM,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;AAE1C,QAAA,aAAa,CAAC,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC;AACvD,QAAA,aAAa,CAAC,aAAa,GAAG,aAAa,CAAC;AAC5C,QAAA,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC;AACxC,QAAA,aAAa,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAClC,QAAA,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC;AAC/B,QAAA,aAAa,CAAC,KAAK,GAAG,QAAQ,CAAC;AAE/B,QAAA,OAAO,aAAa,CAAC;KACxB,CAAA;AAED;;;AAGG;IACI,aAAe,CAAA,eAAA,GAAtB,UAAuB,MAAc,EAAA;QAEjC,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,OAAO,KAAK,CAAC;AAChB,SAAA;AAED,QAAA,QACI,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC;AACtC,YAAA,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC;AACpC,YAAA,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACvC,YAAA,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;AAC9B,YAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AACjC,YAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC/B,MAAM,CAAC,gBAAgB,CAAC,KAAK,cAAc,CAAC,QAAQ,EACtD;KACL,CAAA;IACL,OAAC,aAAA,CAAA;AAAD,CAjDA,CAAmC,gBAAgB,CAiDlD;;;;"}