// ====================================================================================
// SCRIPT:         Microsoft Graph Email Downloader (v3 - Enhanced Debugging)
// ====================================================================================

// --- 1. Import Necessary Libraries ---
require('dotenv').config();
const msal = require('@azure/msal-node');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const puppeteer = require('puppeteer');

// --- NEW DEBUGGING SECTION ---
// This will print exactly what is being read from your .env file.
console.log("\n--- DEBUGGING .env VALUES ---");
console.log(`TENANT_ID read as: [${process.env.TENANT_ID}]`);
console.log(`CLIENT_ID read as: [${process.env.CLIENT_ID}]`);
console.log(`CLIENT_SECRET read as: [${process.env.CLIENT_SECRET}]`);
console.log(`CLIENT_SECRET length: ${process.env.CLIENT_SECRET ? process.env.CLIENT_SECRET.length : 0}`);
console.log("--- END DEBUGGING ---\n");
// --- END NEW DEBUGGING SECTION ---


// --- 2. Validate and Load Configuration ---
console.log("Loading configuration from .env file...");
const requiredEnvVars = ['CLIENT_ID', 'CLIENT_SECRET', 'TENANT_ID', 'MAIL_USER_ID', 'TARGET_FOLDER_NAME', 'SAVE_PATH'];
for (const varName of requiredEnvVars) {
    if (!process.env[varName]) {
        console.error(`\n[FATAL ERROR] Environment variable "${varName}" is missing or empty in your .env file.`);
        console.error("Please check your .env file and try again.");
        process.exit(1);
    }
}
const config = {
    auth: {
        clientId: process.env.CLIENT_ID,
        authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
        clientSecret: process.env.CLIENT_SECRET
    },
    system: {
        loggerOptions: {
            loggerCallback(loglevel, message, containsPii) {
                console.log(message);
            },
            piiLoggingEnabled: false,
            logLevel: 3, // LogLevel.Info
        }
    },
    mail: { userId: process.env.MAIL_USER_ID, folderName: process.env.TARGET_FOLDER_NAME, },
    savePath: process.env.SAVE_PATH,
};
console.log("Configuration loaded successfully.");


// --- 3. Authentication Function ---
async function getAccessToken() {
    console.log("Creating MSAL configuration...");

    // Create a clean configuration object
    const msalConfig = {
        auth: {
            clientId: process.env.CLIENT_ID,
            authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
            clientSecret: process.env.CLIENT_SECRET
        }
    };

    console.log("MSAL Config:", JSON.stringify(msalConfig, null, 2));
    console.log("Client Secret length:", process.env.CLIENT_SECRET?.length);
    console.log("Client Secret exists:", !!process.env.CLIENT_SECRET);

    try {
        const cca = new msal.ConfidentialClientApplication(msalConfig);
        console.log("MSAL client created successfully.");

        // Use Microsoft Graph scope for accessing emails
        const tokenRequest = { scopes: ['https://graph.microsoft.com/.default'] };
        const response = await cca.acquireTokenByClientCredential(tokenRequest);
        console.log("Successfully acquired access token.");
        return response.accessToken;
    } catch (error) {
        console.error("Error in getAccessToken:", error);
        console.error("Error stack:", error.stack);
        throw new Error("Authentication failed. Check your TENANT_ID, CLIENT_ID, and CLIENT_SECRET.");
    }
}

// --- 4. Helper Functions ---
function sanitizeFileName(name) {
    if (!name) return "Untitled";
    return name.replace(/[\\/:*?"<>|]/g, '-').trim();
}
async function getFolderId(api, folderName) {
    console.log(`Searching for folder named '${folderName}'...`);
    const filter = `$filter=displayName eq '${folderName}'`;

    // Try with delegated permissions first (using /me endpoint)
    try {
        const url = `/me/mailFolders?${filter}`;
        const response = await api.get(url);
        if (response.data.value.length > 0) {
            const folderId = response.data.value[0].id;
            console.log(`Found folder ID: ${folderId}`);
            return folderId;
        }
    } catch (error) {
        console.log("Delegated permissions failed, trying application permissions...");
    }

    // Fallback to application permissions (using /users/{userId} endpoint)
    const url = `/users/${config.mail.userId}/mailFolders?${filter}`;
    const response = await api.get(url);
    if (response.data.value.length > 0) {
        const folderId = response.data.value[0].id;
        console.log(`Found folder ID: ${folderId}`);
        return folderId;
    } else {
        throw new Error(`Mail folder '${folderName}' not found for user '${config.mail.userId}'.`);
    }
}
async function saveBodyAsPdf(message, fullFolderPath) {
    const sanitizedSubject = sanitizeFileName(message.subject);
    const pdfPath = path.join(fullFolderPath, `Email - ${sanitizedSubject}.pdf`);
    console.log(`  -> Saving email body as PDF: ${path.basename(pdfPath)}`);
    const browser = await puppeteer.launch({ args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    await page.setContent(message.body.content, { waitUntil: 'networkidle0' });
    await page.pdf({ path: pdfPath, format: 'A4', printBackground: true });
    await browser.close();
}
async function saveAttachments(api, message, fullFolderPath) {
    if (!message.hasAttachments) return;
    console.log("  -> Checking for attachments...");

    // Try delegated permissions first, then fallback to application permissions
    let url = `/me/messages/${message.id}/attachments`;
    try {
        const response = await api.get(url);
        const attachments = response.data.value;
        for (const attachment of attachments) {
            const isImage = /\.(jpg|jpeg|png|gif|bmp|ico)$/i.test(attachment.name);
            const minImageSize = 102400; // 100 KB
            if (isImage && attachment.size < minImageSize) {
                console.log(`     - Skipping small image: ${attachment.name} (${attachment.size} bytes)`);
                continue;
            }
            const attachmentPath = path.join(fullFolderPath, sanitizeFileName(attachment.name));
            console.log(`     - Saving attachment: ${attachment.name}`);
            const contentBytes = Buffer.from(attachment.contentBytes, 'base64');
            await fs.writeFile(attachmentPath, contentBytes);
        }
    } catch (error) {
        // Fallback to application permissions
        console.log("  -> Trying with application permissions...");
        url = `/users/${config.mail.userId}/messages/${message.id}/attachments`;
        const response = await api.get(url);
        const attachments = response.data.value;
        for (const attachment of attachments) {
            const isImage = /\.(jpg|jpeg|png|gif|bmp|ico)$/i.test(attachment.name);
            const minImageSize = 102400; // 100 KB
            if (isImage && attachment.size < minImageSize) {
                console.log(`     - Skipping small image: ${attachment.name} (${attachment.size} bytes)`);
                continue;
            }
            const attachmentPath = path.join(fullFolderPath, sanitizeFileName(attachment.name));
            console.log(`     - Saving attachment: ${attachment.name}`);
            const contentBytes = Buffer.from(attachment.contentBytes, 'base64');
            await fs.writeFile(attachmentPath, contentBytes);
        }
    }
}

// --- 5. Main Execution Logic ---
async function main() {
    console.log("\nStarting email download process...");
    try {
        const accessToken = await getAccessToken();
        const api = axios.create({ baseURL: 'https://graph.microsoft.com/v1.0', headers: { Authorization: `Bearer ${accessToken}` } });
        const folderId = await getFolderId(api, config.mail.folderName);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const dateFilter = `$filter=receivedDateTime ge ${yesterday.toISOString()} and receivedDateTime lt ${today.toISOString()}`;
        console.log("Retrieving emails from yesterday...");

        // Try delegated permissions first, then fallback to application permissions
        let messageResponse;
        try {
            const messageUrl = `/me/mailFolders/${folderId}/messages?${dateFilter}`;
            messageResponse = await api.get(messageUrl);
        } catch (error) {
            console.log("Delegated permissions failed for messages, trying application permissions...");
            const messageUrl = `/users/${config.mail.userId}/mailFolders/${folderId}/messages?${dateFilter}`;
            messageResponse = await api.get(messageUrl);
        }
        const messages = messageResponse.data.value;
        if (messages.length === 0) {
            console.log("No new emails found from yesterday. Process complete.");
            return;
        }
        console.log(`Found ${messages.length} email(s) to process.`);
        for (const message of messages) {
            const subject = message.subject || "No Subject";
            console.log(`\nProcessing email: "${subject}"`);
            const sanitizedSubject = sanitizeFileName(subject);
            const emailFolderPath = path.join(config.savePath, sanitizedSubject);
            await fs.mkdir(emailFolderPath, { recursive: true });
            await saveBodyAsPdf(message, emailFolderPath);
            await saveAttachments(api, message, emailFolderPath);
        }
        console.log("\nProcess finished successfully!");
    } catch (error) {
        console.error("\n[PROCESS FAILED]");
        if (error.response && error.response.data) {
            console.error("API Error:", JSON.stringify(error.response.data, null, 2));
        } else {
            console.error("Error details:", error.message);
        }
    }
}

main();