{"version": 3, "file": "PublicClientApplication.mjs", "sources": ["../../src/client/PublicClientApplication.ts"], "sourcesContent": [null], "names": ["CommonConstants"], "mappings": ";;;;;;;;;;AAAA;;;AAGG;AAuCH;;;;AAIG;AACG,MAAO,uBACT,SAAQ,iBAAiB,CAAA;AAKzB;;;;;;;;;;;;;;;;AAgBG;AACH,IAAA,WAAA,CAAY,aAA4B,EAAA;QACpC,KAAK,CAAC,aAAa,CAAC,CAAC;AACrB,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE;YACvC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,iBAAiB,EAAE;gBACzD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC;AAChE,gBAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CACnC,CAAC;AACL,aAAA;AAAM,iBAAA;AACH,gBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,yEAAyE,CAC5E,CAAC;AACL,aAAA;AACJ,SAAA;AACD,QAAA,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,kBAAkB,CAAC;YAClD,WAAW,EAAE,SAAS,CAAC,QAAQ;AAC/B,YAAA,cAAc,EAAE,OAAO;AAC1B,SAAA,CAAC,CAAC;KACN;AAED;;;;;;;;AAQG;IACI,MAAM,wBAAwB,CACjC,OAA0B,EAAA;QAE1B,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,iCAAiC,EACjC,OAAO,CAAC,aAAa,CACxB,CAAC;AACF,QAAA,MAAM,YAAY,GAA4B,MAAM,CAAC,MAAM,CACvD,OAAO,EACP,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAC5C,CAAC;AACF,QAAA,MAAM,sBAAsB,GAAG,IAAI,CAAC,gCAAgC,CAChE,KAAK,CAAC,wBAAwB,EAC9B,YAAY,CAAC,aAAa,CAC7B,CAAC;QACF,IAAI;YACA,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAC,eAAe,CAClD,YAAY,CAAC,SAAS,EACtB,YAAY,CAAC,aAAa,EAC1B,SAAS,EACT,OAAO,CAAC,iBAAiB,CAC5B,CAAC;AACF,YAAA,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,6BAA6B,CAC7D,mBAAmB,EACnB,YAAY,CAAC,aAAa,EAC1B,EAAE,EACF,sBAAsB,CACzB,CAAC;AACF,YAAA,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YAChE,IAAI,CAAC,MAAM,CAAC,OAAO,CACf,4BAA4B,EAC5B,YAAY,CAAC,aAAa,CAC7B,CAAC;AACF,YAAA,OAAO,MAAM,gBAAgB,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC5D,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,YAAY,SAAS,EAAE;AACxB,gBAAA,CAAC,CAAC,gBAAgB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;AAClD,aAAA;AACD,YAAA,sBAAsB,CAAC,kBAAkB,CAAC,CAAc,CAAC,CAAC;AAC1D,YAAA,MAAM,CAAC,CAAC;AACX,SAAA;KACJ;AAED;;AAEG;IACH,MAAM,uBAAuB,CACzB,OAA2B,EAAA;AAE3B,QAAA,MAAM,aAAa,GACf,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;QACjE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,aAAa,CAAC,CAAC;AACnE,QAAA,MAAM,EACF,WAAW,EACX,eAAe,EACf,aAAa,EACb,YAAY,EACZ,cAAc,EAAE,oBAAoB,EACpC,GAAG,mBAAmB,EACzB,GAAG,OAAO,CAAC;QAEZ,IAAI,IAAI,CAAC,kBAAkB,EAAE;AACzB,YAAA,MAAM,aAAa,GAAkB;AACjC,gBAAA,GAAG,mBAAmB;AACtB,gBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;AACnC,gBAAA,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,mBAAmB;gBAC7C,WAAW,EAAE,GAAG,SAAS,CAAC,aAAa,CAAG,EAAA,SAAS,CAAC,SAAS,CAAE,CAAA;gBAC/D,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS;AAC1D,gBAAA,aAAa,EAAE,aAAa;AAC5B,gBAAA,eAAe,EAAE;oBACb,GAAG,mBAAmB,CAAC,oBAAoB;oBAC3C,GAAG,mBAAmB,CAAC,oBAAoB;AAC3C,oBAAA,CAAC,kBAAkB,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI;AACrD,iBAAA;AACD,gBAAA,SAAS,EAAE,mBAAmB,CAAC,OAAO,EAAE,eAAe;aAC1D,CAAC;YACF,OAAO,IAAI,CAAC,kBAAkB,CAAC,uBAAuB,CAClD,aAAa,EACb,YAAY,CACf,CAAC;AACL,SAAA;AAED,QAAA,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GACzB,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;AAElD,QAAA,MAAM,cAAc,GAChB,oBAAoB,IAAI,IAAI,cAAc,EAAE,CAAC;QAEjD,IAAI,gBAAgB,GAAsB,EAAE,CAAC;QAC7C,IAAI,qBAAqB,GAAqB,IAAI,CAAC;QACnD,IAAI;YACA,MAAM,gBAAgB,GAAG,cAAc;AAClC,iBAAA,iBAAiB,CAAC,eAAe,EAAE,aAAa,CAAC;AACjD,iBAAA,IAAI,CAAC,CAAC,QAAQ,KAAI;gBACf,gBAAgB,GAAG,QAAQ,CAAC;AAChC,aAAC,CAAC;AACD,iBAAA,KAAK,CAAC,CAAC,CAAC,KAAI;;gBAET,qBAAqB,GAAG,CAAC,CAAC;AAC9B,aAAC,CAAC,CAAC;;YAGP,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;AAElE,YAAA,MAAM,YAAY,GAA4B;AAC1C,gBAAA,GAAG,mBAAmB;AACtB,gBAAA,aAAa,EAAE,aAAa;AAC5B,gBAAA,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,mBAAmB;AAC7C,gBAAA,WAAW,EAAE,WAAW;gBACxB,YAAY,EAAE,YAAY,CAAC,KAAK;AAChC,gBAAA,aAAa,EAAE,SAAS;gBACxB,mBAAmB,EAAE,yBAAyB,CAAC,IAAI;aACtD,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;AAC5D,YAAA,MAAM,WAAW,CAAC,WAAW,CAAC,CAAC;AAC/B,YAAA,MAAM,gBAAgB,CAAC;AACvB,YAAA,IAAI,qBAAqB,EAAE;AACvB,gBAAA,MAAM,qBAAqB,CAAC;AAC/B,aAAA;YAED,IAAI,gBAAgB,CAAC,KAAK,EAAE;AACxB,gBAAA,MAAM,IAAI,WAAW,CACjB,gBAAgB,CAAC,KAAK,EACtB,gBAAgB,CAAC,iBAAiB,EAClC,gBAAgB,CAAC,QAAQ,CAC5B,CAAC;AACL,aAAA;AAAM,iBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE;AAC/B,gBAAA,MAAM,aAAa,CAAC,+BAA+B,EAAE,CAAC;AACzD,aAAA;AAED,YAAA,MAAM,UAAU,GAAG,gBAAgB,CAAC,WAAW,CAAC;AAChD,YAAA,MAAM,YAAY,GAA6B;gBAC3C,IAAI,EAAE,gBAAgB,CAAC,IAAI;AAC3B,gBAAA,YAAY,EAAE,QAAQ;AACtB,gBAAA,UAAU,EAAE,UAAU,IAAIA,WAAe,CAAC,YAAY;AACtD,gBAAA,GAAG,YAAY;aAClB,CAAC;YACF,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AACtD,SAAA;AAAS,gBAAA;YACN,cAAc,CAAC,WAAW,EAAE,CAAC;AAChC,SAAA;KACJ;AAED;;;;AAIG;IACH,MAAM,kBAAkB,CACpB,OAA0B,EAAA;AAE1B,QAAA,MAAM,aAAa,GACf,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;QACjE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,aAAa,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,kBAAkB,EAAE;AACzB,YAAA,MAAM,aAAa,GAAkB;AACjC,gBAAA,GAAG,OAAO;AACV,gBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;AACnC,gBAAA,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,mBAAmB;gBAC7C,WAAW,EAAE,GAAG,SAAS,CAAC,aAAa,CAAG,EAAA,SAAS,CAAC,SAAS,CAAE,CAAA;gBAC/D,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS;AAC1D,gBAAA,aAAa,EAAE,aAAa;AAC5B,gBAAA,eAAe,EAAE;oBACb,GAAG,OAAO,CAAC,oBAAoB;AAC/B,oBAAA,CAAC,kBAAkB,CAAC,kBAAkB,GAAG,IAAI,CAAC,IAAI;AACrD,iBAAA;AACD,gBAAA,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,eAAe;AAC1C,gBAAA,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,KAAK;aAC9C,CAAC;YACF,OAAO,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;AACpE,SAAA;AAED,QAAA,OAAO,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;KAC5C;AAED;;;;AAIG;IACH,MAAM,OAAO,CAAC,OAAuB,EAAA;QACjC,IAAI,IAAI,CAAC,kBAAkB,IAAI,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE;AAC5D,YAAA,MAAM,cAAc,GAAyB;AACzC,gBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;AACnC,gBAAA,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,eAAe;gBAC1C,aAAa,EACT,OAAO,CAAC,aAAa;AACrB,oBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE;aAC1C,CAAC;YACF,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACzD,SAAA;AAED,QAAA,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC,aAAa,CACpC,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,aAAa,CACxB,CAAC;KACL;AAED;;;AAGG;AACH,IAAA,MAAM,cAAc,GAAA;QAChB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;AAC1D,YAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,CACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACzB,aAAa,CAChB,CAAC;AACL,SAAA;AAED,QAAA,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,cAAc,EAAE,CAAC;KAChD;AAED;;;;AAIG;IACK,MAAM,kBAAkB,CAC5B,cAA+B,EAAA;QAE/B,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,KAAI;YAC3C,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,YAAA,MAAM,EAAE,GAAG,WAAW,CAAC,MAAK;gBACxB,IACI,yBAAyB,CAAC,UAAU;AAChC,oBAAA,yBAAyB,CAAC,WAAW;AACzC,oBAAA,KAAK,EACP;oBACE,aAAa,CAAC,EAAE,CAAC,CAAC;AAClB,oBAAA,MAAM,CAAC,aAAa,CAAC,gCAAgC,EAAE,CAAC,CAAC;oBACzD,OAAO;AACV,iBAAA;gBAED,IAAI;AACA,oBAAA,MAAM,CAAC,GAAG,cAAc,CAAC,cAAc,EAAE,CAAC;oBAC1C,aAAa,CAAC,EAAE,CAAC,CAAC;oBAClB,OAAO,CAAC,CAAC,CAAC,CAAC;oBACX,OAAO;AACV,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;oBACR,IACI,CAAC,YAAY,SAAS;AACtB,wBAAA,CAAC,CAAC,SAAS;AACP,4BAAA,oBAAoB,CAAC,sBAAsB,CAAC,IAAI,EACtD;;AAEE,wBAAA,KAAK,EAAE,CAAC;wBACR,OAAO;AACV,qBAAA;oBACD,aAAa,CAAC,EAAE,CAAC,CAAC;oBAClB,MAAM,CAAC,CAAC,CAAC,CAAC;oBACV,OAAO;AACV,iBAAA;AACL,aAAC,EAAE,yBAAyB,CAAC,WAAW,CAAC,CAAC;AAC9C,SAAC,CAAC,CAAC;KACN;AACJ;;;;"}