// Modified Email Downloader using Delegated Permissions (User Authentication)
require('dotenv').config();
const msal = require('@azure/msal-node');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const puppeteer = require('puppeteer');

// Debug environment variables
console.log("\n--- DEBUGGING .env VALUES ---");
console.log(`TENANT_ID read as: [${process.env.TENANT_ID}]`);
console.log(`CLIENT_ID read as: [${process.env.CLIENT_ID}]`);
console.log(`CLIENT_SECRET read as: [${process.env.CLIENT_SECRET}]`);
console.log(`CLIENT_SECRET length: ${process.env.CLIENT_SECRET?.length}`);
console.log("--- END DEBUGGING ---\n");

// Validate and Load Configuration
console.log("Loading configuration from .env file...");
const requiredEnvVars = ['CLIENT_ID', 'CLIENT_SECRET', 'TENANT_ID', 'MAIL_USER_ID', 'TARGET_FOLDER_NAME', 'SAVE_PATH'];
for (const varName of requiredEnvVars) {
    if (!process.env[varName]) {
        console.error(`\n[FATAL ERROR] Environment variable "${varName}" is missing or empty in your .env file.`);
        console.error("Please check your .env file and try again.");
        process.exit(1);
    }
}

const config = {
    auth: {
        clientId: process.env.CLIENT_ID,
        authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
        clientSecret: process.env.CLIENT_SECRET,
        redirectUri: "http://localhost:3000/auth/callback"
    },
    mail: { userId: process.env.MAIL_USER_ID, folderName: process.env.TARGET_FOLDER_NAME },
    savePath: process.env.SAVE_PATH,
};
console.log("Configuration loaded successfully.");

// Authentication Function using Device Code Flow (no browser needed)
async function getAccessToken() {
    console.log("Creating MSAL configuration for delegated permissions...");
    
    const msalConfig = {
        auth: {
            clientId: process.env.CLIENT_ID,
            authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
        }
    };
    
    try {
        const pca = new msal.PublicClientApplication(msalConfig);
        
        // Use device code flow - user will need to authenticate once
        const deviceCodeRequest = {
            scopes: ['https://graph.microsoft.com/Mail.Read', 'https://graph.microsoft.com/User.Read'],
            deviceCodeCallback: (response) => {
                console.log("\n" + "=".repeat(60));
                console.log("USER AUTHENTICATION REQUIRED");
                console.log("=".repeat(60));
                console.log(`1. Open a web browser and go to: ${response.verificationUri}`);
                console.log(`2. Enter this code: ${response.userCode}`);
                console.log(`3. Sign in with the account: ${process.env.MAIL_USER_ID}`);
                console.log("4. Come back here and wait for authentication to complete...");
                console.log("=".repeat(60) + "\n");
            }
        };
        
        console.log("Starting device code authentication...");
        const response = await pca.acquireTokenByDeviceCode(deviceCodeRequest);
        console.log("✅ Authentication successful!");
        return response.accessToken;
        
    } catch (error) {
        console.error("Authentication failed:", error.message);
        throw new Error("Authentication failed. Please try again.");
    }
}

// Helper Functions (same as before)
function sanitizeFileName(name) {
    if (!name) return "Untitled";
    return name.replace(/[\\/:*?"<>|]/g, '-').trim();
}

async function getFolderId(api, folderName) {
    console.log(`Searching for folder named '${folderName}'...`);
    const filter = `$filter=displayName eq '${folderName}'`;
    const url = `/me/mailFolders?${filter}`;  // Changed to /me/ for delegated permissions
    const response = await api.get(url);
    if (response.data.value.length > 0) {
        const folderId = response.data.value[0].id;
        console.log(`Found folder ID: ${folderId}`);
        return folderId;
    } else {
        throw new Error(`Mail folder '${folderName}' not found.`);
    }
}

async function saveBodyAsPdf(message, fullFolderPath) {
    const sanitizedSubject = sanitizeFileName(message.subject);
    const pdfPath = path.join(fullFolderPath, `Email - ${sanitizedSubject}.pdf`);
    console.log(`  -> Saving email body as PDF: ${path.basename(pdfPath)}`);
    const browser = await puppeteer.launch({ args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    await page.setContent(message.body.content, { waitUntil: 'networkidle0' });
    await page.pdf({ path: pdfPath, format: 'A4', printBackground: true });
    await browser.close();
}

async function saveAttachments(api, message, fullFolderPath) {
    if (!message.hasAttachments) return;
    console.log("  -> Checking for attachments...");
    const url = `/me/messages/${message.id}/attachments`;  // Changed to /me/ for delegated permissions
    const response = await api.get(url);
    const attachments = response.data.value;
    for (const attachment of attachments) {
        const isImage = /\.(jpg|jpeg|png|gif|bmp|ico)$/i.test(attachment.name);
        const minImageSize = 102400; // 100 KB
        if (isImage && attachment.size < minImageSize) {
            console.log(`     - Skipping small image: ${attachment.name} (${attachment.size} bytes)`);
            continue;
        }
        const attachmentPath = path.join(fullFolderPath, sanitizeFileName(attachment.name));
        console.log(`     - Saving attachment: ${attachment.name}`);
        const contentBytes = Buffer.from(attachment.contentBytes, 'base64');
        await fs.writeFile(attachmentPath, contentBytes);
    }
}

// Main Execution Logic
async function main() {
    console.log("\nStarting email download process...");
    try {
        const accessToken = await getAccessToken();
        const api = axios.create({ 
            baseURL: 'https://graph.microsoft.com/v1.0', 
            headers: { Authorization: `Bearer ${accessToken}` } 
        });
        
        const folderId = await getFolderId(api, config.mail.folderName);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const dateFilter = `$filter=receivedDateTime ge ${yesterday.toISOString()} and receivedDateTime lt ${today.toISOString()}`;
        console.log("Retrieving emails from yesterday...");
        const messageUrl = `/me/mailFolders/${folderId}/messages?${dateFilter}`;  // Changed to /me/
        const messageResponse = await api.get(messageUrl);
        const messages = messageResponse.data.value;
        
        if (messages.length === 0) {
            console.log("No new emails found from yesterday. Process complete.");
            return;
        }
        
        console.log(`Found ${messages.length} email(s) to process.`);
        for (const message of messages) {
            const subject = message.subject || "No Subject";
            console.log(`\nProcessing email: "${subject}"`);
            const sanitizedSubject = sanitizeFileName(subject);
            const emailFolderPath = path.join(config.savePath, sanitizedSubject);
            await fs.mkdir(emailFolderPath, { recursive: true });
            await saveBodyAsPdf(message, emailFolderPath);
            await saveAttachments(api, message, emailFolderPath);
        }
        console.log("\nProcess finished successfully!");
        
    } catch (error) {
        console.error("\n[PROCESS FAILED]");
        if (error.response && error.response.data) {
            console.error("API Error:", JSON.stringify(error.response.data, null, 2));
        } else {
            console.error("Error details:", error.message);
        }
    }
}

main();
