/**
 * This is a helper class that parses supported HTTP response authentication headers to extract and return
 * header challenge values that can be used outside the basic authorization flows.
 */
export declare class AuthenticationHeaderParser {
    private headers;
    constructor(headers: Record<string, string>);
    /**
     * This method parses the SHR nonce value out of either the Authentication-Info or WWW-Authenticate authentication headers.
     * @returns
     */
    getShrNonce(): string;
    /**
     * Parses an HTTP header's challenge set into a key/value map.
     * @param header
     * @returns
     */
    private parseChallenges;
}
//# sourceMappingURL=AuthenticationHeaderParser.d.ts.map