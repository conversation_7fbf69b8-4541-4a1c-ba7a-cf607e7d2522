{"version": 3, "file": "CacheError.mjs", "sources": ["../../src/error/CacheError.ts"], "sourcesContent": [null], "names": ["CacheErrorCodes.cacheQuotaExceeded", "CacheErrorCodes.cacheErrorUnknown"], "mappings": ";;;;;;;AAAA;;;AAGG;AAMU,MAAA,kBAAkB,GAAG;AAC9B,IAAA,CAACA,kBAAkC,GAAG,kCAAkC;AACxE,IAAA,CAACC,iBAAiC,GAC9B,qDAAqD;EAC3D;AAEF;;AAEG;AACG,MAAO,UAAW,SAAQ,SAAS,CAAA;IAWrC,WAAY,CAAA,SAAiB,EAAE,YAAqB,EAAA;QAChD,MAAM,OAAO,GACT,YAAY;aACX,kBAAkB,CAAC,SAAS,CAAC;AAC1B,kBAAE,kBAAkB,CAAC,SAAS,CAAC;kBAC7B,kBAAkB,CAACA,iBAAiC,CAAC,CAAC,CAAC;AAEjE,QAAA,KAAK,CAAC,CAAG,EAAA,SAAS,KAAK,OAAO,CAAA,CAAE,CAAC,CAAC;QAClC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;AAElD,QAAA,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;KAC/B;AACJ,CAAA;AAED;;;;AAIG;AACG,SAAU,gBAAgB,CAAC,CAAU,EAAA;AACvC,IAAA,IAAI,EAAE,CAAC,YAAY,KAAK,CAAC,EAAE;AACvB,QAAA,OAAO,IAAI,UAAU,CAACA,iBAAiC,CAAC,CAAC;AAC5D,KAAA;AAED,IAAA,IACI,CAAC,CAAC,IAAI,KAAK,oBAAoB;QAC/B,CAAC,CAAC,IAAI,KAAK,4BAA4B;AACvC,QAAA,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAC1C;AACE,QAAA,OAAO,IAAI,UAAU,CAACD,kBAAkC,CAAC,CAAC;AAC7D,KAAA;AAAM,SAAA;QACH,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;AAC5C,KAAA;AACL;;;;"}