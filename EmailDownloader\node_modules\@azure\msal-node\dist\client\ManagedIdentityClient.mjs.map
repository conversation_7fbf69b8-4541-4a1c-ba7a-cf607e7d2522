{"version": 3, "file": "ManagedIdentityClient.mjs", "sources": ["../../src/client/ManagedIdentityClient.ts"], "sourcesContent": [null], "names": ["ManagedIdentityErrorCodes.unableToCreateSource"], "mappings": ";;;;;;;;;;;;AAAA;;;AAGG;AAyBH;;;AAGG;MACU,qBAAqB,CAAA;IAU9B,WACI,CAAA,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAC9B,sBAA+B,EAAA;AAE/B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AACrC,QAAA,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;KACxD;IAEM,MAAM,+BAA+B,CACxC,sBAA8C,EAC9C,iBAAoC,EACpC,aAAwB,EACxB,kBAA4B,EAAA;AAE5B,QAAA,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE;AACvC,YAAA,qBAAqB,CAAC,cAAc;gBAChC,IAAI,CAAC,2BAA2B,CAC5B,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,sBAAsB,EAC3B,iBAAiB,CACpB,CAAC;AACT,SAAA;AAED,QAAA,OAAO,qBAAqB,CAAC,cAAc,CAAC,+BAA+B,CACvE,sBAAsB,EACtB,iBAAiB,EACjB,aAAa,EACb,kBAAkB,CACrB,CAAC;KACL;AAEO,IAAA,iCAAiC,CACrC,oBAA+C,EAAA;AAE/C,QAAA,OAAO,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,KAAK,CAC5C,CAAC,mBAAmB,KAAI;YACpB,OAAO,mBAAmB,KAAK,SAAS,CAAC;AAC7C,SAAC,CACJ,CAAC;KACL;AAED;;;AAGG;IACI,wBAAwB,GAAA;AAC3B,QAAA,qBAAqB,CAAC,UAAU;AAC5B,YAAA,IAAI,CAAC,iCAAiC,CAClC,aAAa,CAAC,uBAAuB,EAAE,CAC1C;kBACK,0BAA0B,CAAC,cAAc;kBACzC,IAAI,CAAC,iCAAiC,CAClC,UAAU,CAAC,uBAAuB,EAAE,CACvC;sBACD,0BAA0B,CAAC,WAAW;sBACtC,IAAI,CAAC,iCAAiC,CAClC,eAAe,CAAC,uBAAuB,EAAE,CAC5C;0BACD,0BAA0B,CAAC,gBAAgB;0BAC3C,IAAI,CAAC,iCAAiC,CAClC,UAAU,CAAC,uBAAuB,EAAE,CACvC;8BACD,0BAA0B,CAAC,WAAW;8BACtC,IAAI,CAAC,iCAAiC,CAClC,QAAQ,CAAC,uBAAuB,EAAE,CACrC;kCACD,0BAA0B,CAAC,SAAS;AACtC,kCAAE,0BAA0B,CAAC,eAAe,CAAC;QAErD,OAAO,qBAAqB,CAAC,UAAU,CAAC;KAC3C;AAED;;;AAGG;IACK,2BAA2B,CAC/B,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAC9B,sBAA+B,EAC/B,iBAAoC,EAAA;AAEpC,QAAA,MAAM,MAAM,GACR,aAAa,CAAC,SAAS,CACnB,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,EACtB,iBAAiB,CACpB;AACD,YAAA,UAAU,CAAC,SAAS,CAChB,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,CACzB;AACD,YAAA,eAAe,CAAC,SAAS,CACrB,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,CACzB;AACD,YAAA,UAAU,CAAC,SAAS,CAChB,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,EACtB,iBAAiB,CACpB;AACD,YAAA,QAAQ,CAAC,SAAS,CACd,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,EACtB,iBAAiB,CACpB;AACD,YAAA,IAAI,CAAC,SAAS,CACV,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,CACzB,CAAC;QACN,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,MAAM,0BAA0B,CAC5BA,oBAA8C,CACjD,CAAC;AACL,SAAA;AACD,QAAA,OAAO,MAAM,CAAC;KACjB;AACJ;;;;"}