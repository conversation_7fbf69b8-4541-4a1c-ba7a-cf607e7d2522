{"version": 3, "file": "ThrottlingUtils.js", "sources": ["../../src/network/ThrottlingUtils.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { NetworkResponse } from \"./NetworkManager\";\r\nimport { ServerAuthorizationTokenResponse } from \"../response/ServerAuthorizationTokenResponse\";\r\nimport { HeaderNames, ThrottlingConstants, Constants } from \"../utils/Constants\";\r\nimport { CacheManager } from \"../cache/CacheManager\";\r\nimport { ServerError } from \"../error/ServerError\";\r\nimport { RequestThumbprint } from \"./RequestThumbprint\";\r\nimport { ThrottlingEntity } from \"../cache/entities/ThrottlingEntity\";\r\nimport { BaseAuthRequest } from \"../request/BaseAuthRequest\";\r\n\r\nexport class ThrottlingUtils {\r\n\r\n    /**\r\n     * Prepares a RequestThumbprint to be stored as a key.\r\n     * @param thumbprint\r\n     */\r\n    static generateThrottlingStorageKey(thumbprint: RequestThumbprint): string {\r\n        return `${ThrottlingConstants.THROTTLING_PREFIX}.${JSON.stringify(thumbprint)}`;\r\n    }\r\n\r\n    /**\r\n     * Performs necessary throttling checks before a network request.\r\n     * @param cacheManager\r\n     * @param thumbprint\r\n     */\r\n    static preProcess(cacheManager: CacheManager, thumbprint: RequestThumbprint): void {\r\n        const key = ThrottlingUtils.generateThrottlingStorageKey(thumbprint);\r\n        const value = cacheManager.getThrottlingCache(key);\r\n\r\n        if (value) {\r\n            if (value.throttleTime < Date.now()) {\r\n                cacheManager.removeItem(key);\r\n                return;\r\n            }\r\n            throw new ServerError(value.errorCodes?.join(\" \") || Constants.EMPTY_STRING, value.errorMessage, value.subError);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Performs necessary throttling checks after a network request.\r\n     * @param cacheManager\r\n     * @param thumbprint\r\n     * @param response\r\n     */\r\n    static postProcess(cacheManager: CacheManager, thumbprint: RequestThumbprint, response: NetworkResponse<ServerAuthorizationTokenResponse>): void {\r\n        if (ThrottlingUtils.checkResponseStatus(response) || ThrottlingUtils.checkResponseForRetryAfter(response)) {\r\n            const thumbprintValue: ThrottlingEntity = {\r\n                throttleTime: ThrottlingUtils.calculateThrottleTime(parseInt(response.headers[HeaderNames.RETRY_AFTER])),\r\n                error: response.body.error,\r\n                errorCodes: response.body.error_codes,\r\n                errorMessage: response.body.error_description,\r\n                subError: response.body.suberror\r\n            };\r\n            cacheManager.setThrottlingCache(\r\n                ThrottlingUtils.generateThrottlingStorageKey(thumbprint),\r\n                thumbprintValue\r\n            );\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Checks a NetworkResponse object's status codes against 429 or 5xx\r\n     * @param response\r\n     */\r\n    static checkResponseStatus(response: NetworkResponse<ServerAuthorizationTokenResponse>): boolean {\r\n        return response.status === 429 || response.status >= 500 && response.status < 600;\r\n    }\r\n\r\n    /**\r\n     * Checks a NetworkResponse object's RetryAfter header\r\n     * @param response\r\n     */\r\n    static checkResponseForRetryAfter(response: NetworkResponse<ServerAuthorizationTokenResponse>): boolean {\r\n        if (response.headers) {\r\n            return response.headers.hasOwnProperty(HeaderNames.RETRY_AFTER) && (response.status < 200 || response.status >= 300);\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Calculates the Unix-time value for a throttle to expire given throttleTime in seconds.\r\n     * @param throttleTime\r\n     */\r\n    static calculateThrottleTime(throttleTime: number): number {\r\n        const time = throttleTime <= 0 ? 0 : throttleTime;\r\n\r\n        const currentSeconds = Date.now() / 1000;\r\n        return Math.floor(Math.min(\r\n            currentSeconds + (time || ThrottlingConstants.DEFAULT_THROTTLE_TIME_SECONDS),\r\n            currentSeconds + ThrottlingConstants.DEFAULT_MAX_THROTTLE_TIME_SECONDS\r\n        ) * 1000);\r\n    }\r\n\r\n    static removeThrottle(cacheManager: CacheManager, clientId: string, request: BaseAuthRequest, homeAccountIdentifier?: string): void {\r\n        const thumbprint: RequestThumbprint = {\r\n            clientId: clientId,\r\n            authority: request.authority,\r\n            scopes: request.scopes,\r\n            homeAccountIdentifier: homeAccountIdentifier,\r\n            claims: request.claims,\r\n            authenticationScheme: request.authenticationScheme,\r\n            resourceRequestMethod: request.resourceRequestMethod,\r\n            resourceRequestUri: request.resourceRequestUri,\r\n            shrClaims: request.shrClaims,\r\n            sshKid: request.sshKid\r\n        };\r\n\r\n        const key = this.generateThrottlingStorageKey(thumbprint);\r\n        cacheManager.removeItem(key);\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAWH,IAAA,eAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,eAAA,GAAA;KAoGC;AAlGG;;;AAGG;IACI,eAA4B,CAAA,4BAAA,GAAnC,UAAoC,UAA6B,EAAA;QAC7D,OAAU,mBAAmB,CAAC,iBAAiB,GAAI,GAAA,GAAA,IAAI,CAAC,SAAS,CAAC,UAAU,CAAG,CAAC;KACnF,CAAA;AAED;;;;AAIG;AACI,IAAA,eAAA,CAAA,UAAU,GAAjB,UAAkB,YAA0B,EAAE,UAA6B,EAAA;;QACvE,IAAM,GAAG,GAAG,eAAe,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;QACrE,IAAM,KAAK,GAAG,YAAY,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AAEnD,QAAA,IAAI,KAAK,EAAE;YACP,IAAI,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE;AACjC,gBAAA,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC7B,OAAO;AACV,aAAA;YACD,MAAM,IAAI,WAAW,CAAC,CAAA,CAAA,EAAA,GAAA,KAAK,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,CAAC,GAAG,CAAA,KAAK,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpH,SAAA;KACJ,CAAA;AAED;;;;;AAKG;AACI,IAAA,eAAA,CAAA,WAAW,GAAlB,UAAmB,YAA0B,EAAE,UAA6B,EAAE,QAA2D,EAAA;AACrI,QAAA,IAAI,eAAe,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,eAAe,CAAC,0BAA0B,CAAC,QAAQ,CAAC,EAAE;AACvG,YAAA,IAAM,eAAe,GAAqB;AACtC,gBAAA,YAAY,EAAE,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC;AACxG,gBAAA,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;AAC1B,gBAAA,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW;AACrC,gBAAA,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,iBAAiB;AAC7C,gBAAA,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;aACnC,CAAC;AACF,YAAA,YAAY,CAAC,kBAAkB,CAC3B,eAAe,CAAC,4BAA4B,CAAC,UAAU,CAAC,EACxD,eAAe,CAClB,CAAC;AACL,SAAA;KACJ,CAAA;AAED;;;AAGG;IACI,eAAmB,CAAA,mBAAA,GAA1B,UAA2B,QAA2D,EAAA;AAClF,QAAA,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;KACrF,CAAA;AAED;;;AAGG;IACI,eAA0B,CAAA,0BAAA,GAAjC,UAAkC,QAA2D,EAAA;QACzF,IAAI,QAAQ,CAAC,OAAO,EAAE;YAClB,OAAO,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC;AACxH,SAAA;AACD,QAAA,OAAO,KAAK,CAAC;KAChB,CAAA;AAED;;;AAGG;IACI,eAAqB,CAAA,qBAAA,GAA5B,UAA6B,YAAoB,EAAA;AAC7C,QAAA,IAAM,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;QAElD,IAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;QACzC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CACtB,cAAc,IAAI,IAAI,IAAI,mBAAmB,CAAC,6BAA6B,CAAC,EAC5E,cAAc,GAAG,mBAAmB,CAAC,iCAAiC,CACzE,GAAG,IAAI,CAAC,CAAC;KACb,CAAA;IAEM,eAAc,CAAA,cAAA,GAArB,UAAsB,YAA0B,EAAE,QAAgB,EAAE,OAAwB,EAAE,qBAA8B,EAAA;AACxH,QAAA,IAAM,UAAU,GAAsB;AAClC,YAAA,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;AACtB,YAAA,qBAAqB,EAAE,qBAAqB;YAC5C,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;YAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;YACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;YAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;SACzB,CAAC;QAEF,IAAM,GAAG,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;AAC1D,QAAA,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;KAChC,CAAA;IACL,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA;;;;"}