{"version": 3, "file": "CcsCredential.js", "sources": ["../../src/account/CcsCredential.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nexport type CcsCredential = {\r\n    credential: string,\r\n    type: CcsCredentialType\r\n};\r\n\r\nexport enum CcsCredentialType {\r\n    HOME_ACCOUNT_ID = \"home_account_id\",\r\n    UPN = \"UPN\"\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;IAOS,kBAGX;AAHD,CAAA,UAAY,iBAAiB,EAAA;AACzB,IAAA,iBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;AACnC,IAAA,iBAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACf,CAAC,EAHW,iBAAiB,KAAjB,iBAAiB,GAG5B,EAAA,CAAA,CAAA;;;;"}