{"name": "@azure/msal-common", "author": {"name": "Microsoft", "email": "<EMAIL>", "url": "https://www.microsoft.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/AzureAD/microsoft-authentication-library-for-js.git"}, "version": "13.3.1", "description": "Microsoft Authentication Library for js", "keywords": ["implicit", "authorization code", "PKCE", "js", "AAD", "msal", "o<PERSON>h"], "sideEffects": false, "main": "./dist/index.cjs.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=0.8.0"}, "directories": {"test": "test"}, "files": ["dist"], "scripts": {"clean": "shx rm -rf dist lib", "clean:coverage": "rimraf ../../.nyc_output/*", "lint": "cd ../../ && npm run lint:common", "lint:fix": "npm run lint -- -- --fix", "test": "jest", "test:coverage": "jest --coverage", "test:coverage:only": "npm run clean:coverage && npm run test:coverage", "build:modules": "rollup -c", "build:modules:watch": "rollup -cw", "build": "npm run clean && npm run build:modules", "build:all": "npm run build", "prepack": "npm run build", "metadata:check": "npx ts-node scripts/metadata.ts"}, "devDependencies": {"@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/preset-env": "^7.7.1", "@babel/preset-typescript": "^7.7.2", "@types/debug": "^4.1.5", "@types/jest": "^27.0.0", "@types/lodash": "^4.14.182", "@types/sinon": "^7.5.0", "husky": "^3.0.9", "jest": "^27.0.4", "lodash": "^4.17.21", "rimraf": "^3.0.2", "rollup": "^2.46.0", "rollup-plugin-typescript2": "^0.29.0", "shx": "^0.3.2", "sinon": "^7.5.0", "ts-jest": "^27.1.5", "tslib": "^1.10.0", "tslint": "^5.20.0", "typescript": "^3.7.5", "yargs": "^17.5.1", "@types/node": "^18.0.0"}}