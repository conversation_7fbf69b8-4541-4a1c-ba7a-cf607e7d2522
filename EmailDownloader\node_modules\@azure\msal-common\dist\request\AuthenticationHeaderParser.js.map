{"version": 3, "file": "AuthenticationHeaderParser.js", "sources": ["../../src/request/AuthenticationHeaderParser.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\r\nimport { Constants, HeaderNames } from \"../utils/Constants\";\r\n\r\ntype WWWAuthenticateChallenges = {\r\n    nonce?: string,\r\n};\r\n\r\ntype AuthenticationInfoChallenges = {\r\n    nextnonce?: string\r\n};\r\n\r\n/**\r\n * This is a helper class that parses supported HTTP response authentication headers to extract and return\r\n * header challenge values that can be used outside the basic authorization flows.\r\n */\r\nexport class AuthenticationHeaderParser {\r\n    private headers: Record<string, string>;\r\n\r\n    constructor(headers: Record<string, string>) {\r\n        this.headers = headers;\r\n    }\r\n\r\n    /**\r\n     * This method parses the SHR nonce value out of either the Authentication-Info or WWW-Authenticate authentication headers.\r\n     * @returns \r\n     */\r\n    getShrNonce(): string {\r\n        // Attempt to parse nonce from Authentiacation-Info\r\n        const authenticationInfo = this.headers[HeaderNames.AuthenticationInfo];\r\n        if (authenticationInfo) {\r\n            const authenticationInfoChallenges = this.parseChallenges<AuthenticationInfoChallenges>(authenticationInfo);\r\n            if (authenticationInfoChallenges.nextnonce) {\r\n                return authenticationInfoChallenges.nextnonce;\r\n            }\r\n            throw ClientConfigurationError.createInvalidAuthenticationHeaderError(HeaderNames.AuthenticationInfo, \"nextnonce challenge is missing.\");\r\n        }\r\n\r\n        // Attempt to parse nonce from WWW-Authenticate\r\n        const wwwAuthenticate = this.headers[HeaderNames.WWWAuthenticate];\r\n        if (wwwAuthenticate) {\r\n            const wwwAuthenticateChallenges = this.parseChallenges<WWWAuthenticateChallenges>(wwwAuthenticate);     \r\n            if (wwwAuthenticateChallenges.nonce){\r\n                return wwwAuthenticateChallenges.nonce;\r\n            }\r\n            throw ClientConfigurationError.createInvalidAuthenticationHeaderError(HeaderNames.WWWAuthenticate, \"nonce challenge is missing.\");\r\n        }\r\n\r\n        // If neither header is present, throw missing headers error\r\n        throw ClientConfigurationError.createMissingNonceAuthenticationHeadersError();\r\n    }\r\n\r\n    /**\r\n     * Parses an HTTP header's challenge set into a key/value map.\r\n     * @param header \r\n     * @returns \r\n     */\r\n    private parseChallenges<T>(header: string): T {\r\n        const schemeSeparator = header.indexOf(\" \");\r\n        const challenges = header.substr(schemeSeparator + 1).split(\",\");\r\n        const challengeMap = {} as T;\r\n\r\n        challenges.forEach((challenge: string) => {\r\n            const [ key, value ] = challenge.split(\"=\");\r\n            // Remove escaped quotation marks (', \") from challenge string to keep only the challenge value\r\n            challengeMap[key] = unescape(value.replace(/['\"]+/g, Constants.EMPTY_STRING));\r\n        });\r\n\r\n        return challengeMap;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAaH;;;AAGG;AACH,IAAA,0BAAA,kBAAA,YAAA;AAGI,IAAA,SAAA,0BAAA,CAAY,OAA+B,EAAA;AACvC,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;KAC1B;AAED;;;AAGG;AACH,IAAA,0BAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;;QAEI,IAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AACxE,QAAA,IAAI,kBAAkB,EAAE;YACpB,IAAM,4BAA4B,GAAG,IAAI,CAAC,eAAe,CAA+B,kBAAkB,CAAC,CAAC;YAC5G,IAAI,4BAA4B,CAAC,SAAS,EAAE;gBACxC,OAAO,4BAA4B,CAAC,SAAS,CAAC;AACjD,aAAA;YACD,MAAM,wBAAwB,CAAC,sCAAsC,CAAC,WAAW,CAAC,kBAAkB,EAAE,iCAAiC,CAAC,CAAC;AAC5I,SAAA;;QAGD,IAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AAClE,QAAA,IAAI,eAAe,EAAE;YACjB,IAAM,yBAAyB,GAAG,IAAI,CAAC,eAAe,CAA4B,eAAe,CAAC,CAAC;YACnG,IAAI,yBAAyB,CAAC,KAAK,EAAC;gBAChC,OAAO,yBAAyB,CAAC,KAAK,CAAC;AAC1C,aAAA;YACD,MAAM,wBAAwB,CAAC,sCAAsC,CAAC,WAAW,CAAC,eAAe,EAAE,6BAA6B,CAAC,CAAC;AACrI,SAAA;;AAGD,QAAA,MAAM,wBAAwB,CAAC,4CAA4C,EAAE,CAAC;KACjF,CAAA;AAED;;;;AAIG;IACK,0BAAe,CAAA,SAAA,CAAA,eAAA,GAAvB,UAA2B,MAAc,EAAA;QACrC,IAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC5C,QAAA,IAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACjE,IAAM,YAAY,GAAG,EAAO,CAAC;AAE7B,QAAA,UAAU,CAAC,OAAO,CAAC,UAAC,SAAiB,EAAA;AAC3B,YAAA,IAAA,EAAiB,GAAA,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAnC,GAAG,GAAA,EAAA,CAAA,CAAA,CAAA,EAAE,KAAK,QAAyB,CAAC;;AAE5C,YAAA,YAAY,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;AAClF,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,YAAY,CAAC;KACvB,CAAA;IACL,OAAC,0BAAA,CAAA;AAAD,CAAC,EAAA;;;;"}