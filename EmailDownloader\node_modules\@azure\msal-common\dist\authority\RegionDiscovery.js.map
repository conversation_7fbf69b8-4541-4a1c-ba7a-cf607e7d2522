{"version": 3, "file": "RegionDiscovery.js", "sources": ["../../src/authority/RegionDiscovery.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { INetworkModule } from \"../network/INetworkModule\";\r\nimport { NetworkResponse } from \"../network/NetworkManager\";\r\nimport { IMDSBadResponse } from \"../response/IMDSBadResponse\";\r\nimport { Constants, RegionDiscoverySources, ResponseCodes } from \"../utils/Constants\";\r\nimport { RegionDiscoveryMetadata } from \"./RegionDiscoveryMetadata\";\r\nimport { ImdsOptions } from \"./ImdsOptions\";\r\nimport { IPerformanceClient } from \"../telemetry/performance/IPerformanceClient\";\r\nimport { PerformanceEvents } from \"../telemetry/performance/PerformanceEvent\";\r\n\r\nexport class RegionDiscovery {\r\n    // Network interface to make requests with.\r\n    protected networkInterface: INetworkModule;\r\n    // Performance client\r\n    protected performanceClient: IPerformanceClient | undefined;\r\n    // CorrelationId\r\n    protected correlationId: string | undefined;\r\n    // Options for the IMDS endpoint request\r\n    protected static IMDS_OPTIONS: ImdsOptions = {\r\n        headers: {\r\n            Metadata: \"true\",\r\n        },\r\n    };\r\n\r\n    constructor(networkInterface: INetworkModule, performanceClient?: IPerformanceClient, correlationId?: string) {\r\n        this.networkInterface = networkInterface;\r\n        this.performanceClient = performanceClient;\r\n        this.correlationId = correlationId;\r\n    }\r\n\r\n    /**\r\n     * Detect the region from the application's environment.\r\n     * \r\n     * @returns Promise<string | null>\r\n     */\r\n    public async detectRegion(environmentRegion: string | undefined, regionDiscoveryMetadata: RegionDiscoveryMetadata): Promise<string | null> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.RegionDiscoveryDetectRegion, this.correlationId);\r\n        \r\n        // Initialize auto detected region with the region from the envrionment \r\n        let autodetectedRegionName = environmentRegion;\r\n\r\n        // Check if a region was detected from the environment, if not, attempt to get the region from IMDS \r\n        if (!autodetectedRegionName) {\r\n            const options = RegionDiscovery.IMDS_OPTIONS;\r\n\r\n            try {\r\n                this.performanceClient?.setPreQueueTime(PerformanceEvents.RegionDiscoveryGetRegionFromIMDS, this.correlationId);\r\n                const localIMDSVersionResponse = await this.getRegionFromIMDS(Constants.IMDS_VERSION, options);\r\n                if (localIMDSVersionResponse.status === ResponseCodes.httpSuccess) {\r\n                    autodetectedRegionName = localIMDSVersionResponse.body;\r\n                    regionDiscoveryMetadata.region_source = RegionDiscoverySources.IMDS;\r\n                } \r\n                \r\n                // If the response using the local IMDS version failed, try to fetch the current version of IMDS and retry. \r\n                if (localIMDSVersionResponse.status === ResponseCodes.httpBadRequest) {\r\n                    this.performanceClient?.setPreQueueTime(PerformanceEvents.RegionDiscoveryGetCurrentVersion, this.correlationId);\r\n                    const currentIMDSVersion = await this.getCurrentVersion(options);\r\n                    if (!currentIMDSVersion) {\r\n                        regionDiscoveryMetadata.region_source = RegionDiscoverySources.FAILED_AUTO_DETECTION;\r\n                        return null;\r\n                    }\r\n\r\n                    this.performanceClient?.setPreQueueTime(PerformanceEvents.RegionDiscoveryGetRegionFromIMDS, this.correlationId);\r\n                    const currentIMDSVersionResponse = await this.getRegionFromIMDS(currentIMDSVersion, options);\r\n                    if (currentIMDSVersionResponse.status === ResponseCodes.httpSuccess) {\r\n                        autodetectedRegionName = currentIMDSVersionResponse.body;\r\n                        regionDiscoveryMetadata.region_source = RegionDiscoverySources.IMDS;\r\n                    }\r\n                }\r\n            } catch(e) {\r\n                regionDiscoveryMetadata.region_source = RegionDiscoverySources.FAILED_AUTO_DETECTION;\r\n                return null;\r\n            } \r\n        } else {\r\n            regionDiscoveryMetadata.region_source = RegionDiscoverySources.ENVIRONMENT_VARIABLE;\r\n        }\r\n\r\n        // If no region was auto detected from the environment or from the IMDS endpoint, mark the attempt as a FAILED_AUTO_DETECTION\r\n        if (!autodetectedRegionName) {\r\n            regionDiscoveryMetadata.region_source = RegionDiscoverySources.FAILED_AUTO_DETECTION;\r\n        }\r\n\r\n        return autodetectedRegionName || null;\r\n    }\r\n\r\n    /**\r\n     * Make the call to the IMDS endpoint\r\n     * \r\n     * @param imdsEndpointUrl\r\n     * @returns Promise<NetworkResponse<string>>\r\n     */\r\n    private async getRegionFromIMDS(version: string, options: ImdsOptions): Promise<NetworkResponse<string>> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.RegionDiscoveryGetRegionFromIMDS, this.correlationId);\r\n        return this.networkInterface.sendGetRequestAsync<string>(`${Constants.IMDS_ENDPOINT}?api-version=${version}&format=text`, options, Constants.IMDS_TIMEOUT);\r\n    }\r\n\r\n    /**\r\n     * Get the most recent version of the IMDS endpoint available\r\n     *  \r\n     * @returns Promise<string | null>\r\n     */\r\n    private async getCurrentVersion(options: ImdsOptions): Promise<string | null> {\r\n        this.performanceClient?.addQueueMeasurement(PerformanceEvents.RegionDiscoveryGetCurrentVersion, this.correlationId);\r\n        try {\r\n            const response = await this.networkInterface.sendGetRequestAsync<IMDSBadResponse>(`${Constants.IMDS_ENDPOINT}?format=json`, options);\r\n\r\n            // When IMDS endpoint is called without the api version query param, bad request response comes back with latest version.\r\n            if (response.status === ResponseCodes.httpBadRequest && response.body && response.body[\"newest-versions\"] && response.body[\"newest-versions\"].length > 0) {\r\n                return response.body[\"newest-versions\"][0];\r\n            }\r\n\r\n            return null;\r\n        } catch (e) {\r\n            return null;\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAWH,IAAA,eAAA,kBAAA,YAAA;AAcI,IAAA,SAAA,eAAA,CAAY,gBAAgC,EAAE,iBAAsC,EAAE,aAAsB,EAAA;AACxG,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;KACtC;AAED;;;;AAIG;AACU,IAAA,eAAA,CAAA,SAAA,CAAA,YAAY,GAAzB,UAA0B,iBAAqC,EAAE,uBAAgD,EAAA;;;;;;;AAC7G,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,2BAA2B,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;wBAG3G,sBAAsB,GAAG,iBAAiB,CAAC;6BAG3C,CAAC,sBAAsB,EAAvB,OAAuB,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACjB,wBAAA,OAAO,GAAG,eAAe,CAAC,YAAY,CAAC;;;;AAGzC,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;wBAC/E,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA,CAAA;;AAAxF,wBAAA,wBAAwB,GAAG,EAA6D,CAAA,IAAA,EAAA,CAAA;AAC9F,wBAAA,IAAI,wBAAwB,CAAC,MAAM,KAAK,aAAa,CAAC,WAAW,EAAE;AAC/D,4BAAA,sBAAsB,GAAG,wBAAwB,CAAC,IAAI,CAAC;AACvD,4BAAA,uBAAuB,CAAC,aAAa,GAAG,sBAAsB,CAAC,IAAI,CAAC;AACvE,yBAAA;8BAGG,wBAAwB,CAAC,MAAM,KAAK,aAAa,CAAC,cAAc,CAAA,EAAhE,OAAgE,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AAChE,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AACrF,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA,CAAA;;AAA1D,wBAAA,kBAAkB,GAAG,EAAqC,CAAA,IAAA,EAAA,CAAA;wBAChE,IAAI,CAAC,kBAAkB,EAAE;AACrB,4BAAA,uBAAuB,CAAC,aAAa,GAAG,sBAAsB,CAAC,qBAAqB,CAAC;AACrF,4BAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;AACf,yBAAA;AAED,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,eAAe,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;wBAC7E,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAA,CAAA;;AAAtF,wBAAA,0BAA0B,GAAG,EAAyD,CAAA,IAAA,EAAA,CAAA;AAC5F,wBAAA,IAAI,0BAA0B,CAAC,MAAM,KAAK,aAAa,CAAC,WAAW,EAAE;AACjE,4BAAA,sBAAsB,GAAG,0BAA0B,CAAC,IAAI,CAAC;AACzD,4BAAA,uBAAuB,CAAC,aAAa,GAAG,sBAAsB,CAAC,IAAI,CAAC;AACvE,yBAAA;;;;;AAGL,wBAAA,uBAAuB,CAAC,aAAa,GAAG,sBAAsB,CAAC,qBAAqB,CAAC;AACrF,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;AAGhB,wBAAA,uBAAuB,CAAC,aAAa,GAAG,sBAAsB,CAAC,oBAAoB,CAAC;;;;wBAIxF,IAAI,CAAC,sBAAsB,EAAE;AACzB,4BAAA,uBAAuB,CAAC,aAAa,GAAG,sBAAsB,CAAC,qBAAqB,CAAC;AACxF,yBAAA;wBAED,OAAO,CAAA,CAAA,aAAA,sBAAsB,IAAI,IAAI,CAAC,CAAA;;;;AACzC,KAAA,CAAA;AAED;;;;;AAKG;AACW,IAAA,eAAA,CAAA,SAAA,CAAA,iBAAiB,GAA/B,UAAgC,OAAe,EAAE,OAAoB,EAAA;;;;AACjE,gBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;AACpH,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAY,SAAS,CAAC,aAAa,qBAAgB,OAAO,GAAA,cAAc,EAAE,OAAO,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC,CAAA;;;AAC9J,KAAA,CAAA;AAED;;;;AAIG;IACW,eAAiB,CAAA,SAAA,CAAA,iBAAA,GAA/B,UAAgC,OAAoB,EAAA;;;;;;;AAChD,wBAAA,CAAA,EAAA,GAAA,IAAI,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,mBAAmB,CAAC,iBAAiB,CAAC,gCAAgC,EAAE,IAAI,CAAC,aAAa,CAAE,CAAA;;;;AAE/F,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAqB,SAAS,CAAC,aAAa,GAAA,cAAc,EAAE,OAAO,CAAC,CAAA,CAAA;;AAA9H,wBAAA,QAAQ,GAAG,EAAmH,CAAA,IAAA,EAAA,CAAA;;AAGpI,wBAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,aAAa,CAAC,cAAc,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;4BACtJ,OAAO,CAAA,CAAA,aAAA,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9C,yBAAA;AAED,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;AAEZ,wBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,CAAA;;;;;AAEnB,KAAA,CAAA;;AAjGgB,IAAA,eAAA,CAAA,YAAY,GAAgB;AACzC,QAAA,OAAO,EAAE;AACL,YAAA,QAAQ,EAAE,MAAM;AACnB,SAAA;KACJ,CAAC;IA8FN,OAAC,eAAA,CAAA;AAAA,CA1GD,EA0GC;;;;"}