{"version": 3, "file": "ClientConfigurationError.d.ts", "sourceRoot": "", "sources": ["../../src/error/ClientConfigurationError.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD;;GAEG;AACH,eAAO,MAAM,+BAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAiG3C,CAAC;AAEF;;GAEG;AACH,qBAAa,wBAAyB,SAAQ,eAAe;gBAE7C,SAAS,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,MAAM;IAMpD;;OAEG;IACH,MAAM,CAAC,2BAA2B,IAAI,wBAAwB;IAK9D;;OAEG;IACH,MAAM,CAAC,qCAAqC,IAAI,wBAAwB;IAKxE;;OAEG;IACH,MAAM,CAAC,+BAA+B,CAAC,uBAAuB,EAAE,MAAM,GAAG,wBAAwB;IAKjG;;;OAGG;IACH,MAAM,CAAC,+BAA+B,CAAC,SAAS,EAAE,MAAM,GAAG,wBAAwB;IAKnF;;;OAGG;IACH,MAAM,CAAC,mBAAmB,CAAC,aAAa,EAAE,MAAM,GAAG,wBAAwB;IAK3E;;;OAGG;IACH,MAAM,CAAC,mBAAmB,IAAI,wBAAwB;IAKtD;;;OAGG;IACH,MAAM,CAAC,2BAA2B,IAAI,wBAAwB;IAK9D;;;OAGG;IACH,MAAM,CAAC,8BAA8B,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,wBAAwB;IAK3F;;;OAGG;IACH,MAAM,CAAC,wBAAwB,CAAC,WAAW,EAAE,MAAM,GAAG,wBAAwB;IAK9E;;OAEG;IACH,MAAM,CAAC,+BAA+B,IAAI,wBAAwB;IAKlE;;OAEG;IACH,MAAM,CAAC,6BAA6B,IAAI,wBAAwB;IAOhE;;OAEG;IACH,MAAM,CAAC,4BAA4B,IAAI,wBAAwB;IAO/D;;OAEG;IACH,MAAM,CAAC,qCAAqC,IAAI,wBAAwB;IAOxE;;OAEG;IACH,MAAM,CAAC,qCAAqC,IAAI,wBAAwB;IAOxE;;OAEG;IACH,MAAM,CAAC,wCAAwC,IAAI,wBAAwB;IAK3E;;OAEG;IACH,MAAM,CAAC,mCAAmC,IAAI,wBAAwB;IAKtE;;OAEG;IACH,MAAM,CAAC,6BAA6B,IAAI,wBAAwB;IAKhE;;OAEG;IACH,MAAM,CAAC,oCAAoC,IAAI,wBAAwB;IAKvE;;OAEG;IACH,MAAM,CAAC,wBAAwB,IAAI,wBAAwB;IAK3D;;OAEG;IACH,MAAM,CAAC,wBAAwB,IAAI,wBAAwB;IAK3D;;OAEG;IACH,MAAM,CAAC,4CAA4C,IAAI,wBAAwB;IAK/E;;OAEG;IACH,MAAM,CAAC,sCAAsC,CAAC,iBAAiB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,wBAAwB;IAKnH;;OAEG;IACH,MAAM,CAAC,4BAA4B,IAAI,wBAAwB;CAGlE"}