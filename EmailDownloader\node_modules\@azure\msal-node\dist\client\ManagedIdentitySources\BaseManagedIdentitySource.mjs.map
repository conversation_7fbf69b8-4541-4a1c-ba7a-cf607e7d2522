{"version": 3, "file": "BaseManagedIdentitySource.mjs", "sources": ["../../../src/client/ManagedIdentitySources/BaseManagedIdentitySource.ts"], "sourcesContent": [null], "names": ["ManagedIdentityErrorCodes.invalidManagedIdentityIdType", "ManagedIdentityErrorCodes\r\n            .MsiEnvironmentVariableUrlMalformedErrorCodes"], "mappings": ";;;;;;;;;AAAA;;;AAGG;AAqCH;;AAEG;AACU,MAAA,gDAAgD,GAAG;AAC5D,IAAA,+BAA+B,EAAE,UAAU;AAC3C,IAAA,0BAA0B,EAAE,WAAW;AACvC,IAAA,0BAA0B,EAAE,WAAW;AACvC,IAAA,iCAAiC,EAAE,YAAY;AAC/C,IAAA,qCAAqC,EAAE,WAAW;EAC3C;MAIW,yBAAyB,CAAA;IAO3C,WACI,CAAA,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAC9B,sBAA+B,EAAA;AAE/B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AACrC,QAAA,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;KACxD;IAOM,MAAM,2BAA2B,CACpC,QAAuD;;IAEvD,cAA8B;;IAE9B,eAAiD;;IAEjD,sBAA6C,EAAA;AAE7C,QAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;KAChD;AAEM,IAAA,sBAAsB,CACzB,QAAuD,EAAA;QAEvD,IAAI,SAAS,EAAE,SAA6B,CAAC;AAC7C,QAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;;YAE1B,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;gBACrC,QAAQ,CAAC,IAAI,CAAC,UAAU;AACpB,oBAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;AAC3D,aAAA;YAED,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;;AAG9D,YAAA,IAAI,SAAS,GAAG,CAAC,GAAG,IAAI,EAAE;AACtB,gBAAA,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;AAC7B,aAAA;AACJ,SAAA;AAED,QAAA,MAAM,mBAAmB,GAAqC;YAC1D,MAAM,EAAE,QAAQ,CAAC,MAAM;;AAGvB,YAAA,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,YAAY;AACxC,YAAA,UAAU,EAAE,SAAS;AACrB,YAAA,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;AAC7B,YAAA,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,UAAU;AACpC,YAAA,UAAU,EAAE,SAAS;;YAGrB,cAAc,EACV,QAAQ,CAAC,IAAI,CAAC,cAAc,IAAI,QAAQ,CAAC,IAAI,CAAC,aAAa;YAC/D,KAAK,EACD,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ;AACnC,kBAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;AACrB,kBAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI;AACnC,YAAA,iBAAiB,EACb,QAAQ,CAAC,IAAI,CAAC,OAAO;AACrB,iBAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ;AACpC,sBAAE,QAAQ,CAAC,IAAI,CAAC,iBAAiB;sBAC/B,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC;AACvC,YAAA,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW;AACtC,YAAA,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,SAAS;AAClC,YAAA,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;SACnC,CAAC;AAEF,QAAA,OAAO,mBAAmB,CAAC;KAC9B;IAEM,MAAM,+BAA+B,CACxC,sBAA8C,EAC9C,iBAAoC,EACpC,aAAwB,EACxB,kBAA4B,EAAA;AAE5B,QAAA,MAAM,cAAc,GAChB,IAAI,CAAC,aAAa,CACd,sBAAsB,CAAC,QAAQ,EAC/B,iBAAiB,CACpB,CAAC;QAEN,IAAI,sBAAsB,CAAC,sBAAsB,EAAE;YAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,CAAuE,oEAAA,EAAA,sBAAsB,CAAC,MAAM,CAAE,CAAA,CACzG,CAAC;YAEF,cAAc,CAAC,eAAe,CAC1B,8BAA8B,CAAC,uBAAuB,CACzD,GAAG,sBAAsB,CAAC,sBAAsB,CAAC;AACrD,SAAA;AAED,QAAA,IAAI,sBAAsB,CAAC,kBAAkB,EAAE,MAAM,EAAE;YACnD,MAAM,kBAAkB,GACpB,sBAAsB,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YAEzD,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,CAAoF,iFAAA,EAAA,kBAAkB,CAAE,CAAA,CAC3G,CAAC;YAEF,cAAc,CAAC,eAAe,CAC1B,8BAA8B,CAAC,MAAM,CACxC,GAAG,kBAAkB,CAAC;AAC1B,SAAA;AAED,QAAA,MAAM,OAAO,GAA2B,cAAc,CAAC,OAAO,CAAC;QAC/D,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,qBAAqB,CAAC;AAEpE,QAAA,MAAM,qBAAqB,GAA0B,EAAE,OAAO,EAAE,CAAC;QAEjE,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,MAAM,EAAE;AACnD,YAAA,qBAAqB,CAAC,IAAI;gBACtB,cAAc,CAAC,2BAA2B,EAAE,CAAC;AACpD,SAAA;AAED;;;;AAIG;AACH,QAAA,MAAM,mBAAmB,GAAmB,IAAI,CAAC,sBAAsB;cACjE,IAAI,CAAC,aAAa;AACpB,cAAE,IAAI,qBAAqB,CACrB,IAAI,CAAC,aAAa,EAClB,cAAc,CAAC,WAAW,EAC1B,IAAI,CAAC,MAAM,CACd,CAAC;AAER,QAAA,MAAM,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AAC5C,QAAA,IAAI,QAAuD,CAAC;QAC5D,IAAI;;AAEA,YAAA,IAAI,cAAc,CAAC,UAAU,KAAK,UAAU,CAAC,IAAI,EAAE;gBAC/C,QAAQ;oBACJ,MAAM,mBAAmB,CAAC,oBAAoB,CAC1C,cAAc,CAAC,UAAU,EAAE,EAC3B,qBAAqB,CACxB,CAAC;;AAET,aAAA;AAAM,iBAAA;gBACH,QAAQ;oBACJ,MAAM,mBAAmB,CAAC,mBAAmB,CACzC,cAAc,CAAC,UAAU,EAAE,EAC3B,qBAAqB,CACxB,CAAC;AACT,aAAA;AACJ,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;YACZ,IAAI,KAAK,YAAY,SAAS,EAAE;AAC5B,gBAAA,MAAM,KAAK,CAAC;AACf,aAAA;AAAM,iBAAA;AACH,gBAAA,MAAM,qBAAqB,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AAClE,aAAA;AACJ,SAAA;QAED,MAAM,eAAe,GAAG,IAAI,eAAe,CACvC,iBAAiB,CAAC,EAAE,EACpB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,MAAM,EACX,IAAI,EACJ,IAAI,CACP,CAAC;AAEF,QAAA,MAAM,mBAAmB,GACrB,MAAM,IAAI,CAAC,2BAA2B,CAClC,QAAQ,EACR,mBAAmB,EACnB,cAAc,EACd,qBAAqB,CACxB,CAAC;AAEN,QAAA,eAAe,CAAC,qBAAqB,CACjC,mBAAmB,EACnB,kBAAkB,CACrB,CAAC;;AAGF,QAAA,OAAO,eAAe,CAAC,yBAAyB,CAC5C,mBAAmB,EACnB,aAAa,EACb,YAAY,EACZ,sBAAsB,CACzB,CAAC;KACL;AAEM,IAAA,iDAAiD,CACpD,qBAA4C,EAC5C,MAAgB,EAChB,WAAqB,EAAA;AAErB,QAAA,QAAQ,qBAAqB;YACzB,KAAK,qBAAqB,CAAC,uBAAuB;AAC9C,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,mCACI,WAAW,GAAG,OAAO,GAAG,OAC5B,CAAA,gDAAA,CAAkD,CACrD,CAAC;;AAEF,gBAAA,OAAO,WAAW;sBACZ,gDAAgD,CAAC,+BAA+B;AAClF,sBAAE,gDAAgD,CAAC,0BAA0B,CAAC;YAEtF,KAAK,qBAAqB,CAAC,yBAAyB;AAChD,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,qEAAqE,CACxE,CAAC;AACF,gBAAA,OAAO,MAAM;sBACP,gDAAgD,CAAC,iCAAiC;AACpF,sBAAE,gDAAgD,CAAC,qCAAqC,CAAC;YAEjG,KAAK,qBAAqB,CAAC,uBAAuB;AAC9C,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CACZ,mEAAmE,CACtE,CAAC;gBACF,OAAO,gDAAgD,CAAC,0BAA0B,CAAC;AACvF,YAAA;AACI,gBAAA,MAAM,0BAA0B,CAC5BA,4BAAsD,CACzD,CAAC;AACT,SAAA;KACJ;;AAEa,yBAAgC,CAAA,gCAAA,GAAG,CAC7C,qBAA6B,EAC7B,WAAmB,EACnB,UAAkB,EAClB,MAAc,KACN;IACR,IAAI;AACA,QAAA,OAAO,IAAI,SAAS,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC;AAC/C,KAAA;AAAC,IAAA,OAAO,KAAK,EAAE;QACZ,MAAM,CAAC,IAAI,CACP,CAAA,mBAAA,EAAsB,UAAU,CAAiD,8CAAA,EAAA,qBAAqB,CAAsC,oCAAA,CAAA,CAC/I,CAAC;QAEF,MAAM,0BAA0B,CAC5BC,4CACiD,CAC7C,qBAAqB,CACxB,CACJ,CAAC;AACL,KAAA;AACL,CAAC;;;;"}