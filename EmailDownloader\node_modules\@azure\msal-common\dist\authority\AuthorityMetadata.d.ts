export declare const rawMetdataJSON: {
    endpointMetadata: {
        "https://login.microsoftonline.com/common/": {
            token_endpoint: string;
            token_endpoint_auth_methods_supported: string[];
            jwks_uri: string;
            response_modes_supported: string[];
            subject_types_supported: string[];
            id_token_signing_alg_values_supported: string[];
            response_types_supported: string[];
            scopes_supported: string[];
            issuer: string;
            request_uri_parameter_supported: boolean;
            userinfo_endpoint: string;
            authorization_endpoint: string;
            device_authorization_endpoint: string;
            http_logout_supported: boolean;
            frontchannel_logout_supported: boolean;
            end_session_endpoint: string;
            claims_supported: string[];
            kerberos_endpoint: string;
            tenant_region_scope: null;
            cloud_instance_name: string;
            cloud_graph_host_name: string;
            msgraph_host: string;
            rbac_url: string;
        };
        "https://login.chinacloudapi.cn/common/": {
            token_endpoint: string;
            token_endpoint_auth_methods_supported: string[];
            jwks_uri: string;
            response_modes_supported: string[];
            subject_types_supported: string[];
            id_token_signing_alg_values_supported: string[];
            response_types_supported: string[];
            scopes_supported: string[];
            issuer: string;
            request_uri_parameter_supported: boolean;
            userinfo_endpoint: string;
            authorization_endpoint: string;
            device_authorization_endpoint: string;
            http_logout_supported: boolean;
            frontchannel_logout_supported: boolean;
            end_session_endpoint: string;
            claims_supported: string[];
            kerberos_endpoint: string;
            tenant_region_scope: null;
            cloud_instance_name: string;
            cloud_graph_host_name: string;
            msgraph_host: string;
            rbac_url: string;
        };
        "https://login.microsoftonline.us/common/": {
            token_endpoint: string;
            token_endpoint_auth_methods_supported: string[];
            jwks_uri: string;
            response_modes_supported: string[];
            subject_types_supported: string[];
            id_token_signing_alg_values_supported: string[];
            response_types_supported: string[];
            scopes_supported: string[];
            issuer: string;
            request_uri_parameter_supported: boolean;
            userinfo_endpoint: string;
            authorization_endpoint: string;
            device_authorization_endpoint: string;
            http_logout_supported: boolean;
            frontchannel_logout_supported: boolean;
            end_session_endpoint: string;
            claims_supported: string[];
            kerberos_endpoint: string;
            tenant_region_scope: null;
            cloud_instance_name: string;
            cloud_graph_host_name: string;
            msgraph_host: string;
            rbac_url: string;
        };
        "https://login.microsoftonline.com/consumers/": {
            token_endpoint: string;
            token_endpoint_auth_methods_supported: string[];
            jwks_uri: string;
            response_modes_supported: string[];
            subject_types_supported: string[];
            id_token_signing_alg_values_supported: string[];
            response_types_supported: string[];
            scopes_supported: string[];
            issuer: string;
            request_uri_parameter_supported: boolean;
            userinfo_endpoint: string;
            authorization_endpoint: string;
            device_authorization_endpoint: string;
            http_logout_supported: boolean;
            frontchannel_logout_supported: boolean;
            end_session_endpoint: string;
            claims_supported: string[];
            kerberos_endpoint: string;
            tenant_region_scope: null;
            cloud_instance_name: string;
            cloud_graph_host_name: string;
            msgraph_host: string;
            rbac_url: string;
        };
        "https://login.chinacloudapi.cn/consumers/": {
            token_endpoint: string;
            token_endpoint_auth_methods_supported: string[];
            jwks_uri: string;
            response_modes_supported: string[];
            subject_types_supported: string[];
            id_token_signing_alg_values_supported: string[];
            response_types_supported: string[];
            scopes_supported: string[];
            issuer: string;
            request_uri_parameter_supported: boolean;
            userinfo_endpoint: string;
            authorization_endpoint: string;
            device_authorization_endpoint: string;
            http_logout_supported: boolean;
            frontchannel_logout_supported: boolean;
            end_session_endpoint: string;
            claims_supported: string[];
            kerberos_endpoint: string;
            tenant_region_scope: null;
            cloud_instance_name: string;
            cloud_graph_host_name: string;
            msgraph_host: string;
            rbac_url: string;
        };
        "https://login.microsoftonline.us/consumers/": {
            token_endpoint: string;
            token_endpoint_auth_methods_supported: string[];
            jwks_uri: string;
            response_modes_supported: string[];
            subject_types_supported: string[];
            id_token_signing_alg_values_supported: string[];
            response_types_supported: string[];
            scopes_supported: string[];
            issuer: string;
            request_uri_parameter_supported: boolean;
            userinfo_endpoint: string;
            authorization_endpoint: string;
            device_authorization_endpoint: string;
            http_logout_supported: boolean;
            frontchannel_logout_supported: boolean;
            end_session_endpoint: string;
            claims_supported: string[];
            kerberos_endpoint: string;
            tenant_region_scope: null;
            cloud_instance_name: string;
            cloud_graph_host_name: string;
            msgraph_host: string;
            rbac_url: string;
        };
        "https://login.microsoftonline.com/organizations/": {
            token_endpoint: string;
            token_endpoint_auth_methods_supported: string[];
            jwks_uri: string;
            response_modes_supported: string[];
            subject_types_supported: string[];
            id_token_signing_alg_values_supported: string[];
            response_types_supported: string[];
            scopes_supported: string[];
            issuer: string;
            request_uri_parameter_supported: boolean;
            userinfo_endpoint: string;
            authorization_endpoint: string;
            device_authorization_endpoint: string;
            http_logout_supported: boolean;
            frontchannel_logout_supported: boolean;
            end_session_endpoint: string;
            claims_supported: string[];
            kerberos_endpoint: string;
            tenant_region_scope: null;
            cloud_instance_name: string;
            cloud_graph_host_name: string;
            msgraph_host: string;
            rbac_url: string;
        };
        "https://login.chinacloudapi.cn/organizations/": {
            token_endpoint: string;
            token_endpoint_auth_methods_supported: string[];
            jwks_uri: string;
            response_modes_supported: string[];
            subject_types_supported: string[];
            id_token_signing_alg_values_supported: string[];
            response_types_supported: string[];
            scopes_supported: string[];
            issuer: string;
            request_uri_parameter_supported: boolean;
            userinfo_endpoint: string;
            authorization_endpoint: string;
            device_authorization_endpoint: string;
            http_logout_supported: boolean;
            frontchannel_logout_supported: boolean;
            end_session_endpoint: string;
            claims_supported: string[];
            kerberos_endpoint: string;
            tenant_region_scope: null;
            cloud_instance_name: string;
            cloud_graph_host_name: string;
            msgraph_host: string;
            rbac_url: string;
        };
        "https://login.microsoftonline.us/organizations/": {
            token_endpoint: string;
            token_endpoint_auth_methods_supported: string[];
            jwks_uri: string;
            response_modes_supported: string[];
            subject_types_supported: string[];
            id_token_signing_alg_values_supported: string[];
            response_types_supported: string[];
            scopes_supported: string[];
            issuer: string;
            request_uri_parameter_supported: boolean;
            userinfo_endpoint: string;
            authorization_endpoint: string;
            device_authorization_endpoint: string;
            http_logout_supported: boolean;
            frontchannel_logout_supported: boolean;
            end_session_endpoint: string;
            claims_supported: string[];
            kerberos_endpoint: string;
            tenant_region_scope: null;
            cloud_instance_name: string;
            cloud_graph_host_name: string;
            msgraph_host: string;
            rbac_url: string;
        };
    };
    instanceDiscoveryMetadata: {
        "https://login.microsoftonline.com/common/": {
            tenant_discovery_endpoint: string;
            "api-version": string;
            metadata: {
                preferred_network: string;
                preferred_cache: string;
                aliases: string[];
            }[];
        };
        "https://login.chinacloudapi.cn/common/": {
            tenant_discovery_endpoint: string;
            "api-version": string;
            metadata: {
                preferred_network: string;
                preferred_cache: string;
                aliases: string[];
            }[];
        };
        "https://login.microsoftonline.us/common/": {
            tenant_discovery_endpoint: string;
            "api-version": string;
            metadata: {
                preferred_network: string;
                preferred_cache: string;
                aliases: string[];
            }[];
        };
        "https://login.microsoftonline.com/consumers/": {
            tenant_discovery_endpoint: string;
            "api-version": string;
            metadata: {
                preferred_network: string;
                preferred_cache: string;
                aliases: string[];
            }[];
        };
        "https://login.chinacloudapi.cn/consumers/": {
            tenant_discovery_endpoint: string;
            "api-version": string;
            metadata: {
                preferred_network: string;
                preferred_cache: string;
                aliases: string[];
            }[];
        };
        "https://login.microsoftonline.us/consumers/": {
            tenant_discovery_endpoint: string;
            "api-version": string;
            metadata: {
                preferred_network: string;
                preferred_cache: string;
                aliases: string[];
            }[];
        };
        "https://login.microsoftonline.com/organizations/": {
            tenant_discovery_endpoint: string;
            "api-version": string;
            metadata: {
                preferred_network: string;
                preferred_cache: string;
                aliases: string[];
            }[];
        };
        "https://login.chinacloudapi.cn/organizations/": {
            tenant_discovery_endpoint: string;
            "api-version": string;
            metadata: {
                preferred_network: string;
                preferred_cache: string;
                aliases: string[];
            }[];
        };
        "https://login.microsoftonline.us/organizations/": {
            tenant_discovery_endpoint: string;
            "api-version": string;
            metadata: {
                preferred_network: string;
                preferred_cache: string;
                aliases: string[];
            }[];
        };
    };
};
export declare const EndpointMetadata: {
    "https://login.microsoftonline.com/common/": {
        token_endpoint: string;
        token_endpoint_auth_methods_supported: string[];
        jwks_uri: string;
        response_modes_supported: string[];
        subject_types_supported: string[];
        id_token_signing_alg_values_supported: string[];
        response_types_supported: string[];
        scopes_supported: string[];
        issuer: string;
        request_uri_parameter_supported: boolean;
        userinfo_endpoint: string;
        authorization_endpoint: string;
        device_authorization_endpoint: string;
        http_logout_supported: boolean;
        frontchannel_logout_supported: boolean;
        end_session_endpoint: string;
        claims_supported: string[];
        kerberos_endpoint: string;
        tenant_region_scope: null;
        cloud_instance_name: string;
        cloud_graph_host_name: string;
        msgraph_host: string;
        rbac_url: string;
    };
    "https://login.chinacloudapi.cn/common/": {
        token_endpoint: string;
        token_endpoint_auth_methods_supported: string[];
        jwks_uri: string;
        response_modes_supported: string[];
        subject_types_supported: string[];
        id_token_signing_alg_values_supported: string[];
        response_types_supported: string[];
        scopes_supported: string[];
        issuer: string;
        request_uri_parameter_supported: boolean;
        userinfo_endpoint: string;
        authorization_endpoint: string;
        device_authorization_endpoint: string;
        http_logout_supported: boolean;
        frontchannel_logout_supported: boolean;
        end_session_endpoint: string;
        claims_supported: string[];
        kerberos_endpoint: string;
        tenant_region_scope: null;
        cloud_instance_name: string;
        cloud_graph_host_name: string;
        msgraph_host: string;
        rbac_url: string;
    };
    "https://login.microsoftonline.us/common/": {
        token_endpoint: string;
        token_endpoint_auth_methods_supported: string[];
        jwks_uri: string;
        response_modes_supported: string[];
        subject_types_supported: string[];
        id_token_signing_alg_values_supported: string[];
        response_types_supported: string[];
        scopes_supported: string[];
        issuer: string;
        request_uri_parameter_supported: boolean;
        userinfo_endpoint: string;
        authorization_endpoint: string;
        device_authorization_endpoint: string;
        http_logout_supported: boolean;
        frontchannel_logout_supported: boolean;
        end_session_endpoint: string;
        claims_supported: string[];
        kerberos_endpoint: string;
        tenant_region_scope: null;
        cloud_instance_name: string;
        cloud_graph_host_name: string;
        msgraph_host: string;
        rbac_url: string;
    };
    "https://login.microsoftonline.com/consumers/": {
        token_endpoint: string;
        token_endpoint_auth_methods_supported: string[];
        jwks_uri: string;
        response_modes_supported: string[];
        subject_types_supported: string[];
        id_token_signing_alg_values_supported: string[];
        response_types_supported: string[];
        scopes_supported: string[];
        issuer: string;
        request_uri_parameter_supported: boolean;
        userinfo_endpoint: string;
        authorization_endpoint: string;
        device_authorization_endpoint: string;
        http_logout_supported: boolean;
        frontchannel_logout_supported: boolean;
        end_session_endpoint: string;
        claims_supported: string[];
        kerberos_endpoint: string;
        tenant_region_scope: null;
        cloud_instance_name: string;
        cloud_graph_host_name: string;
        msgraph_host: string;
        rbac_url: string;
    };
    "https://login.chinacloudapi.cn/consumers/": {
        token_endpoint: string;
        token_endpoint_auth_methods_supported: string[];
        jwks_uri: string;
        response_modes_supported: string[];
        subject_types_supported: string[];
        id_token_signing_alg_values_supported: string[];
        response_types_supported: string[];
        scopes_supported: string[];
        issuer: string;
        request_uri_parameter_supported: boolean;
        userinfo_endpoint: string;
        authorization_endpoint: string;
        device_authorization_endpoint: string;
        http_logout_supported: boolean;
        frontchannel_logout_supported: boolean;
        end_session_endpoint: string;
        claims_supported: string[];
        kerberos_endpoint: string;
        tenant_region_scope: null;
        cloud_instance_name: string;
        cloud_graph_host_name: string;
        msgraph_host: string;
        rbac_url: string;
    };
    "https://login.microsoftonline.us/consumers/": {
        token_endpoint: string;
        token_endpoint_auth_methods_supported: string[];
        jwks_uri: string;
        response_modes_supported: string[];
        subject_types_supported: string[];
        id_token_signing_alg_values_supported: string[];
        response_types_supported: string[];
        scopes_supported: string[];
        issuer: string;
        request_uri_parameter_supported: boolean;
        userinfo_endpoint: string;
        authorization_endpoint: string;
        device_authorization_endpoint: string;
        http_logout_supported: boolean;
        frontchannel_logout_supported: boolean;
        end_session_endpoint: string;
        claims_supported: string[];
        kerberos_endpoint: string;
        tenant_region_scope: null;
        cloud_instance_name: string;
        cloud_graph_host_name: string;
        msgraph_host: string;
        rbac_url: string;
    };
    "https://login.microsoftonline.com/organizations/": {
        token_endpoint: string;
        token_endpoint_auth_methods_supported: string[];
        jwks_uri: string;
        response_modes_supported: string[];
        subject_types_supported: string[];
        id_token_signing_alg_values_supported: string[];
        response_types_supported: string[];
        scopes_supported: string[];
        issuer: string;
        request_uri_parameter_supported: boolean;
        userinfo_endpoint: string;
        authorization_endpoint: string;
        device_authorization_endpoint: string;
        http_logout_supported: boolean;
        frontchannel_logout_supported: boolean;
        end_session_endpoint: string;
        claims_supported: string[];
        kerberos_endpoint: string;
        tenant_region_scope: null;
        cloud_instance_name: string;
        cloud_graph_host_name: string;
        msgraph_host: string;
        rbac_url: string;
    };
    "https://login.chinacloudapi.cn/organizations/": {
        token_endpoint: string;
        token_endpoint_auth_methods_supported: string[];
        jwks_uri: string;
        response_modes_supported: string[];
        subject_types_supported: string[];
        id_token_signing_alg_values_supported: string[];
        response_types_supported: string[];
        scopes_supported: string[];
        issuer: string;
        request_uri_parameter_supported: boolean;
        userinfo_endpoint: string;
        authorization_endpoint: string;
        device_authorization_endpoint: string;
        http_logout_supported: boolean;
        frontchannel_logout_supported: boolean;
        end_session_endpoint: string;
        claims_supported: string[];
        kerberos_endpoint: string;
        tenant_region_scope: null;
        cloud_instance_name: string;
        cloud_graph_host_name: string;
        msgraph_host: string;
        rbac_url: string;
    };
    "https://login.microsoftonline.us/organizations/": {
        token_endpoint: string;
        token_endpoint_auth_methods_supported: string[];
        jwks_uri: string;
        response_modes_supported: string[];
        subject_types_supported: string[];
        id_token_signing_alg_values_supported: string[];
        response_types_supported: string[];
        scopes_supported: string[];
        issuer: string;
        request_uri_parameter_supported: boolean;
        userinfo_endpoint: string;
        authorization_endpoint: string;
        device_authorization_endpoint: string;
        http_logout_supported: boolean;
        frontchannel_logout_supported: boolean;
        end_session_endpoint: string;
        claims_supported: string[];
        kerberos_endpoint: string;
        tenant_region_scope: null;
        cloud_instance_name: string;
        cloud_graph_host_name: string;
        msgraph_host: string;
        rbac_url: string;
    };
};
export declare const InstanceDiscoveryMetadata: {
    "https://login.microsoftonline.com/common/": {
        tenant_discovery_endpoint: string;
        "api-version": string;
        metadata: {
            preferred_network: string;
            preferred_cache: string;
            aliases: string[];
        }[];
    };
    "https://login.chinacloudapi.cn/common/": {
        tenant_discovery_endpoint: string;
        "api-version": string;
        metadata: {
            preferred_network: string;
            preferred_cache: string;
            aliases: string[];
        }[];
    };
    "https://login.microsoftonline.us/common/": {
        tenant_discovery_endpoint: string;
        "api-version": string;
        metadata: {
            preferred_network: string;
            preferred_cache: string;
            aliases: string[];
        }[];
    };
    "https://login.microsoftonline.com/consumers/": {
        tenant_discovery_endpoint: string;
        "api-version": string;
        metadata: {
            preferred_network: string;
            preferred_cache: string;
            aliases: string[];
        }[];
    };
    "https://login.chinacloudapi.cn/consumers/": {
        tenant_discovery_endpoint: string;
        "api-version": string;
        metadata: {
            preferred_network: string;
            preferred_cache: string;
            aliases: string[];
        }[];
    };
    "https://login.microsoftonline.us/consumers/": {
        tenant_discovery_endpoint: string;
        "api-version": string;
        metadata: {
            preferred_network: string;
            preferred_cache: string;
            aliases: string[];
        }[];
    };
    "https://login.microsoftonline.com/organizations/": {
        tenant_discovery_endpoint: string;
        "api-version": string;
        metadata: {
            preferred_network: string;
            preferred_cache: string;
            aliases: string[];
        }[];
    };
    "https://login.chinacloudapi.cn/organizations/": {
        tenant_discovery_endpoint: string;
        "api-version": string;
        metadata: {
            preferred_network: string;
            preferred_cache: string;
            aliases: string[];
        }[];
    };
    "https://login.microsoftonline.us/organizations/": {
        tenant_discovery_endpoint: string;
        "api-version": string;
        metadata: {
            preferred_network: string;
            preferred_cache: string;
            aliases: string[];
        }[];
    };
};
//# sourceMappingURL=AuthorityMetadata.d.ts.map