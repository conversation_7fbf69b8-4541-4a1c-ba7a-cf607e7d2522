{"version": 3, "file": "ThrottlingEntity.js", "sources": ["../../../src/cache/entities/ThrottlingEntity.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ThrottlingConstants } from \"../../utils/Constants\";\r\n\r\nexport class ThrottlingEntity {\r\n    // Unix-time value representing the expiration of the throttle\r\n    throttleTime: number;\r\n    // Information provided by the server\r\n    error?: string;\r\n    errorCodes?: Array<string>;\r\n    errorMessage?: string;\r\n    subError?: string;\r\n\r\n    /**\r\n     * validates if a given cache entry is \"Throttling\", parses <key,value>\r\n     * @param key\r\n     * @param entity\r\n     */\r\n    static isThrottlingEntity(key: string, entity?: object): boolean {\r\n        \r\n        let validateKey: boolean = false;\r\n        if (key) {\r\n            validateKey = key.indexOf(ThrottlingConstants.THROTTLING_PREFIX) === 0;\r\n        }\r\n        \r\n        let validateEntity: boolean = true;\r\n        if (entity) {\r\n            validateEntity = entity.hasOwnProperty(\"throttleTime\");\r\n        }\r\n\r\n        return validateKey && validateEntity;\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGG;AAIH,IAAA,gBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,gBAAA,GAAA;KA4BC;AAnBG;;;;AAIG;AACI,IAAA,gBAAA,CAAA,kBAAkB,GAAzB,UAA0B,GAAW,EAAE,MAAe,EAAA;QAElD,IAAI,WAAW,GAAY,KAAK,CAAC;AACjC,QAAA,IAAI,GAAG,EAAE;YACL,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC1E,SAAA;QAED,IAAI,cAAc,GAAY,IAAI,CAAC;AACnC,QAAA,IAAI,MAAM,EAAE;AACR,YAAA,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC1D,SAAA;QAED,OAAO,WAAW,IAAI,cAAc,CAAC;KACxC,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}