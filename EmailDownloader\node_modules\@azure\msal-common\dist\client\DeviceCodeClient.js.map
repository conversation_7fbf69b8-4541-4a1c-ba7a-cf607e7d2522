{"version": 3, "file": "DeviceCodeClient.js", "sources": ["../../src/client/DeviceCodeClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { DeviceCodeResponse, ServerDeviceCodeResponse } from \"../response/DeviceCodeResponse\";\r\nimport { BaseClient } from \"./BaseClient\";\r\nimport { CommonDeviceCodeRequest } from \"../request/CommonDeviceCodeRequest\";\r\nimport { ClientAuthError } from \"../error/ClientAuthError\";\r\nimport { RequestParameterBuilder } from \"../request/RequestParameterBuilder\";\r\nimport { Constants, GrantType } from \"../utils/Constants\";\r\nimport { ClientConfiguration } from \"../config/ClientConfiguration\";\r\nimport { TimeUtils } from \"../utils/TimeUtils\";\r\nimport { ServerAuthorizationTokenResponse } from \"../response/ServerAuthorizationTokenResponse\";\r\nimport { ResponseHandler } from \"../response/ResponseHandler\";\r\nimport { AuthenticationResult } from \"../response/AuthenticationResult\";\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { RequestThumbprint } from \"../network/RequestThumbprint\";\r\nimport { ServerError } from \"../error/ServerError\";\r\nimport { UrlString } from \"../url/UrlString\";\r\n\r\n/**\r\n * OAuth2.0 Device code client\r\n */\r\nexport class DeviceCodeClient extends BaseClient {\r\n\r\n    constructor(configuration: ClientConfiguration) {\r\n        super(configuration);\r\n    }\r\n\r\n    /**\r\n     * Gets device code from device code endpoint, calls back to with device code response, and\r\n     * polls token endpoint to exchange device code for tokens\r\n     * @param request\r\n     */\r\n    public async acquireToken(request: CommonDeviceCodeRequest): Promise<AuthenticationResult | null> {\r\n        const deviceCodeResponse: DeviceCodeResponse = await this.getDeviceCode(request);\r\n        request.deviceCodeCallback(deviceCodeResponse);\r\n        const reqTimestamp = TimeUtils.nowSeconds();\r\n        const response: ServerAuthorizationTokenResponse = await this.acquireTokenWithDeviceCode(\r\n            request,\r\n            deviceCodeResponse);\r\n\r\n        const responseHandler = new ResponseHandler(\r\n            this.config.authOptions.clientId,\r\n            this.cacheManager,\r\n            this.cryptoUtils,\r\n            this.logger,\r\n            this.config.serializableCache,\r\n            this.config.persistencePlugin\r\n        );\r\n\r\n        // Validate response. This function throws a server error if an error is returned by the server.\r\n        responseHandler.validateTokenResponse(response);\r\n        return await responseHandler.handleServerTokenResponse(\r\n            response,\r\n            this.authority,\r\n            reqTimestamp,\r\n            request\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Creates device code request and executes http GET\r\n     * @param request\r\n     */\r\n    private async getDeviceCode(request: CommonDeviceCodeRequest): Promise<DeviceCodeResponse> {\r\n        const queryParametersString = this.createExtraQueryParameters(request);\r\n        const endpoint = UrlString.appendQueryString(this.authority.deviceCodeEndpoint, queryParametersString);\r\n        const queryString = this.createQueryString(request);\r\n        const headers = this.createTokenRequestHeaders();\r\n        const thumbprint: RequestThumbprint = {\r\n            clientId: this.config.authOptions.clientId,\r\n            authority: request.authority,\r\n            scopes: request.scopes,\r\n            claims: request.claims,\r\n            authenticationScheme: request.authenticationScheme,\r\n            resourceRequestMethod: request.resourceRequestMethod,\r\n            resourceRequestUri: request.resourceRequestUri,\r\n            shrClaims: request.shrClaims,\r\n            sshKid: request.sshKid\r\n        };\r\n\r\n        return this.executePostRequestToDeviceCodeEndpoint(endpoint, queryString, headers, thumbprint);\r\n    }\r\n\r\n    /**\r\n     * Creates query string for the device code request\r\n     * @param request\r\n     */\r\n    createExtraQueryParameters(request: CommonDeviceCodeRequest): string {\r\n        const parameterBuilder = new RequestParameterBuilder();\r\n\r\n        if (request.extraQueryParameters) {\r\n            parameterBuilder.addExtraQueryParameters(request.extraQueryParameters);\r\n        }\r\n\r\n        return parameterBuilder.createQueryString();\r\n    }\r\n\r\n    /**\r\n     * Executes POST request to device code endpoint\r\n     * @param deviceCodeEndpoint\r\n     * @param queryString\r\n     * @param headers\r\n     */\r\n    private async executePostRequestToDeviceCodeEndpoint(\r\n        deviceCodeEndpoint: string,\r\n        queryString: string,\r\n        headers: Record<string, string>,\r\n        thumbprint: RequestThumbprint): Promise<DeviceCodeResponse> {\r\n\r\n        const {\r\n            body: {\r\n                user_code: userCode,\r\n                device_code: deviceCode,\r\n                verification_uri: verificationUri,\r\n                expires_in: expiresIn,\r\n                interval,\r\n                message\r\n            }\r\n        } = await this.networkManager.sendPostRequest<ServerDeviceCodeResponse>(\r\n            thumbprint,\r\n            deviceCodeEndpoint,\r\n            {\r\n                body: queryString,\r\n                headers: headers\r\n            });\r\n\r\n        return {\r\n            userCode,\r\n            deviceCode,\r\n            verificationUri,\r\n            expiresIn,\r\n            interval,\r\n            message\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Create device code endpoint query parameters and returns string\r\n     */\r\n    private createQueryString(request: CommonDeviceCodeRequest): string {\r\n\r\n        const parameterBuilder: RequestParameterBuilder = new RequestParameterBuilder();\r\n\r\n        parameterBuilder.addScopes(request.scopes);\r\n        parameterBuilder.addClientId(this.config.authOptions.clientId);\r\n\r\n        if (request.extraQueryParameters) {\r\n            parameterBuilder.addExtraQueryParameters(request.extraQueryParameters);\r\n        }\r\n\r\n        if (!StringUtils.isEmpty(request.claims) || this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0) {\r\n            parameterBuilder.addClaims(request.claims, this.config.authOptions.clientCapabilities);\r\n        }\r\n\r\n        return parameterBuilder.createQueryString();\r\n    }\r\n\r\n    /**\r\n     * Breaks the polling with specific conditions.\r\n     * @param request CommonDeviceCodeRequest\r\n     * @param deviceCodeResponse DeviceCodeResponse\r\n     */\r\n    private continuePolling(\r\n        deviceCodeExpirationTime: number,\r\n        userSpecifiedTimeout?: number,\r\n        userSpecifiedCancelFlag?: boolean,\r\n    ): boolean {\r\n        if (userSpecifiedCancelFlag) {\r\n            this.logger.error(\"Token request cancelled by setting DeviceCodeRequest.cancel = true\");\r\n            throw ClientAuthError.createDeviceCodeCancelledError();\r\n        } else if (userSpecifiedTimeout && userSpecifiedTimeout < deviceCodeExpirationTime && TimeUtils.nowSeconds() > userSpecifiedTimeout) {\r\n            this.logger.error(`User defined timeout for device code polling reached. The timeout was set for ${userSpecifiedTimeout}`);\r\n            throw ClientAuthError.createUserTimeoutReachedError();\r\n        } else if (TimeUtils.nowSeconds() > deviceCodeExpirationTime) {\r\n            if (userSpecifiedTimeout) {\r\n                this.logger.verbose(`User specified timeout ignored as the device code has expired before the timeout elapsed. The user specified timeout was set for ${userSpecifiedTimeout}`);\r\n            }\r\n            this.logger.error(`Device code expired. Expiration time of device code was ${deviceCodeExpirationTime}`);\r\n            throw ClientAuthError.createDeviceCodeExpiredError();\r\n        }\r\n        return true;\r\n    }\r\n\r\n    /**\r\n     * Creates token request with device code response and polls token endpoint at interval set by the device code\r\n     * response\r\n     * @param request\r\n     * @param deviceCodeResponse\r\n     */\r\n    private async acquireTokenWithDeviceCode(\r\n        request: CommonDeviceCodeRequest,\r\n        deviceCodeResponse: DeviceCodeResponse): Promise<ServerAuthorizationTokenResponse> {\r\n        const queryParametersString = this.createTokenQueryParameters(request);\r\n        const endpoint = UrlString.appendQueryString(this.authority.tokenEndpoint, queryParametersString);\r\n        const requestBody = this.createTokenRequestBody(request, deviceCodeResponse);\r\n        const headers: Record<string, string> = this.createTokenRequestHeaders();\r\n\r\n        const userSpecifiedTimeout = request.timeout ? TimeUtils.nowSeconds() + request.timeout : undefined;\r\n        const deviceCodeExpirationTime = TimeUtils.nowSeconds() + deviceCodeResponse.expiresIn;\r\n        const pollingIntervalMilli = deviceCodeResponse.interval * 1000;\r\n\r\n        /*\r\n         * Poll token endpoint while (device code is not expired AND operation has not been cancelled by\r\n         * setting CancellationToken.cancel = true). POST request is sent at interval set by pollingIntervalMilli\r\n         */\r\n        while (this.continuePolling(deviceCodeExpirationTime, userSpecifiedTimeout, request.cancel)) {\r\n            const thumbprint: RequestThumbprint = {\r\n                clientId: this.config.authOptions.clientId,\r\n                authority: request.authority,\r\n                scopes: request.scopes,\r\n                claims: request.claims,\r\n                authenticationScheme: request.authenticationScheme,\r\n                resourceRequestMethod: request.resourceRequestMethod,\r\n                resourceRequestUri: request.resourceRequestUri,\r\n                shrClaims: request.shrClaims,\r\n                sshKid: request.sshKid\r\n            };\r\n            const response = await this.executePostToTokenEndpoint(\r\n                endpoint,\r\n                requestBody,\r\n                headers,\r\n                thumbprint);\r\n\r\n            if (response.body && response.body.error) {\r\n                // user authorization is pending. Sleep for polling interval and try again\r\n                if(response.body.error === Constants.AUTHORIZATION_PENDING) {\r\n                    this.logger.info(\"Authorization pending. Continue polling.\");\r\n                    await TimeUtils.delay(pollingIntervalMilli);\r\n                } else {\r\n                    // for any other error, throw\r\n                    this.logger.info(\"Unexpected error in polling from the server\");\r\n                    throw ServerError.createPostRequestFailed(response.body.error);\r\n                }\r\n            } else {\r\n                this.logger.verbose(\"Authorization completed successfully. Polling stopped.\");\r\n                return response.body;\r\n            }\r\n        }\r\n\r\n        /*\r\n         * The above code should've thrown by this point, but to satisfy TypeScript,\r\n         * and in the rare case the conditionals in continuePolling() may not catch everything...\r\n         */\r\n        this.logger.error(\"Polling stopped for unknown reasons.\");\r\n        throw ClientAuthError.createDeviceCodeUnknownError();\r\n    }\r\n\r\n    /**\r\n     * Creates query parameters and converts to string.\r\n     * @param request\r\n     * @param deviceCodeResponse\r\n     */\r\n    private createTokenRequestBody(request: CommonDeviceCodeRequest, deviceCodeResponse: DeviceCodeResponse): string {\r\n\r\n        const requestParameters: RequestParameterBuilder = new RequestParameterBuilder();\r\n\r\n        requestParameters.addScopes(request.scopes);\r\n        requestParameters.addClientId(this.config.authOptions.clientId);\r\n        requestParameters.addGrantType(GrantType.DEVICE_CODE_GRANT);\r\n        requestParameters.addDeviceCode(deviceCodeResponse.deviceCode);\r\n        const correlationId = request.correlationId || this.config.cryptoInterface.createNewGuid();\r\n        requestParameters.addCorrelationId(correlationId);\r\n        requestParameters.addClientInfo();\r\n        requestParameters.addLibraryInfo(this.config.libraryInfo);\r\n        requestParameters.addApplicationTelemetry(this.config.telemetry.application);\r\n        requestParameters.addThrottling();\r\n        if (this.serverTelemetryManager) {\r\n            requestParameters.addServerTelemetry(this.serverTelemetryManager);\r\n        }\r\n\r\n        if (!StringUtils.isEmptyObj(request.claims) || this.config.authOptions.clientCapabilities && this.config.authOptions.clientCapabilities.length > 0) {\r\n            requestParameters.addClaims(request.claims, this.config.authOptions.clientCapabilities);\r\n        }\r\n        return requestParameters.createQueryString();\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;;AAGG;AAkBH;;AAEG;AACH,IAAA,gBAAA,kBAAA,UAAA,MAAA,EAAA;IAAsC,SAAU,CAAA,gBAAA,EAAA,MAAA,CAAA,CAAA;AAE5C,IAAA,SAAA,gBAAA,CAAY,aAAkC,EAAA;AAC1C,QAAA,OAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAM,aAAa,CAAC,IAAA,IAAA,CAAA;KACvB;AAED;;;;AAIG;IACU,gBAAY,CAAA,SAAA,CAAA,YAAA,GAAzB,UAA0B,OAAgC,EAAA;;;;;AACP,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA,CAAA;;AAA1E,wBAAA,kBAAkB,GAAuB,EAAiC,CAAA,IAAA,EAAA,CAAA;AAChF,wBAAA,OAAO,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACzC,wBAAA,YAAY,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;wBACO,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,0BAA0B,CACpF,OAAO,EACP,kBAAkB,CAAC,CAAA,CAAA;;AAFjB,wBAAA,QAAQ,GAAqC,EAE5B,CAAA,IAAA,EAAA,CAAA;AAEjB,wBAAA,eAAe,GAAG,IAAI,eAAe,CACvC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAChC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAChC,CAAC;;AAGF,wBAAA,eAAe,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;AACzC,wBAAA,OAAA,CAAA,CAAA,YAAM,eAAe,CAAC,yBAAyB,CAClD,QAAQ,EACR,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,OAAO,CACV,CAAA,CAAA;AALD,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAO,SAKN,CAAC,CAAA;;;;AACL,KAAA,CAAA;AAED;;;AAGG;IACW,gBAAa,CAAA,SAAA,CAAA,aAAA,GAA3B,UAA4B,OAAgC,EAAA;;;;AAClD,gBAAA,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACjE,gBAAA,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,CAAC;AACjG,gBAAA,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC9C,gBAAA,OAAO,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAC3C,gBAAA,UAAU,GAAsB;AAClC,oBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;oBAC1C,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;oBAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;oBACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;oBAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;iBACzB,CAAC;AAEF,gBAAA,OAAA,CAAA,CAAA,aAAO,IAAI,CAAC,sCAAsC,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC,CAAA;;;AAClG,KAAA,CAAA;AAED;;;AAGG;IACH,gBAA0B,CAAA,SAAA,CAAA,0BAAA,GAA1B,UAA2B,OAAgC,EAAA;AACvD,QAAA,IAAM,gBAAgB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAEvD,IAAI,OAAO,CAAC,oBAAoB,EAAE;AAC9B,YAAA,gBAAgB,CAAC,uBAAuB,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC1E,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C,CAAA;AAED;;;;;AAKG;IACW,gBAAsC,CAAA,SAAA,CAAA,sCAAA,GAApD,UACI,kBAA0B,EAC1B,WAAmB,EACnB,OAA+B,EAC/B,UAA6B,EAAA;;;;;4BAWzB,OAAM,CAAA,CAAA,YAAA,IAAI,CAAC,cAAc,CAAC,eAAe,CACzC,UAAU,EACV,kBAAkB,EAClB;AACI,4BAAA,IAAI,EAAE,WAAW;AACjB,4BAAA,OAAO,EAAE,OAAO;AACnB,yBAAA,CAAC,CAAA,CAAA;;wBAdF,EAQA,GAAA,CAAA,SAME,EAAA,IAPD,EANc,QAAQ,GAAA,EAAA,CAAA,SAAA,EACN,UAAU,GAAA,EAAA,CAAA,WAAA,EACL,eAAe,GAAA,EAAA,CAAA,gBAAA,EACrB,SAAS,GAAA,EAAA,CAAA,UAAA,EACrB,QAAQ,GAAA,EAAA,CAAA,QAAA,EACR,OAAO,GAAA,EAAA,CAAA,OAAA,CAAA;wBAUf,OAAO,CAAA,CAAA,aAAA;AACH,gCAAA,QAAQ,EAAA,QAAA;AACR,gCAAA,UAAU,EAAA,UAAA;AACV,gCAAA,eAAe,EAAA,eAAA;AACf,gCAAA,SAAS,EAAA,SAAA;AACT,gCAAA,QAAQ,EAAA,QAAA;AACR,gCAAA,OAAO,EAAA,OAAA;6BACV,CAAC,CAAA;;;;AACL,KAAA,CAAA;AAED;;AAEG;IACK,gBAAiB,CAAA,SAAA,CAAA,iBAAA,GAAzB,UAA0B,OAAgC,EAAA;AAEtD,QAAA,IAAM,gBAAgB,GAA4B,IAAI,uBAAuB,EAAE,CAAC;AAEhF,QAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3C,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE/D,IAAI,OAAO,CAAC,oBAAoB,EAAE;AAC9B,YAAA,gBAAgB,CAAC,uBAAuB,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC1E,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7I,YAAA,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAC1F,SAAA;AAED,QAAA,OAAO,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;KAC/C,CAAA;AAED;;;;AAIG;AACK,IAAA,gBAAA,CAAA,SAAA,CAAA,eAAe,GAAvB,UACI,wBAAgC,EAChC,oBAA6B,EAC7B,uBAAiC,EAAA;AAEjC,QAAA,IAAI,uBAAuB,EAAE;AACzB,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oEAAoE,CAAC,CAAC;AACxF,YAAA,MAAM,eAAe,CAAC,8BAA8B,EAAE,CAAC;AAC1D,SAAA;AAAM,aAAA,IAAI,oBAAoB,IAAI,oBAAoB,GAAG,wBAAwB,IAAI,SAAS,CAAC,UAAU,EAAE,GAAG,oBAAoB,EAAE;YACjI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gFAAiF,GAAA,oBAAsB,CAAC,CAAC;AAC3H,YAAA,MAAM,eAAe,CAAC,6BAA6B,EAAE,CAAC;AACzD,SAAA;AAAM,aAAA,IAAI,SAAS,CAAC,UAAU,EAAE,GAAG,wBAAwB,EAAE;AAC1D,YAAA,IAAI,oBAAoB,EAAE;gBACtB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mIAAoI,GAAA,oBAAsB,CAAC,CAAC;AACnL,aAAA;YACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0DAA2D,GAAA,wBAA0B,CAAC,CAAC;AACzG,YAAA,MAAM,eAAe,CAAC,4BAA4B,EAAE,CAAC;AACxD,SAAA;AACD,QAAA,OAAO,IAAI,CAAC;KACf,CAAA;AAED;;;;;AAKG;AACW,IAAA,gBAAA,CAAA,SAAA,CAAA,0BAA0B,GAAxC,UACI,OAAgC,EAChC,kBAAsC,EAAA;;;;;;AAChC,wBAAA,qBAAqB,GAAG,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;AACjE,wBAAA,QAAQ,GAAG,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;wBAC5F,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;AACvE,wBAAA,OAAO,GAA2B,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAEnE,wBAAA,oBAAoB,GAAG,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC;wBAC9F,wBAAwB,GAAG,SAAS,CAAC,UAAU,EAAE,GAAG,kBAAkB,CAAC,SAAS,CAAC;AACjF,wBAAA,oBAAoB,GAAG,kBAAkB,CAAC,QAAQ,GAAG,IAAI,CAAC;;;6BAMzD,IAAI,CAAC,eAAe,CAAC,wBAAwB,EAAE,oBAAoB,EAAE,OAAO,CAAC,MAAM,CAAC,EAAA,OAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACjF,wBAAA,UAAU,GAAsB;AAClC,4BAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ;4BAC1C,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,MAAM,EAAE,OAAO,CAAC,MAAM;4BACtB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;4BAClD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;4BACpD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB;4BAC9C,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;yBACzB,CAAC;AACe,wBAAA,OAAA,CAAA,CAAA,YAAM,IAAI,CAAC,0BAA0B,CAClD,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,CAAC,CAAA,CAAA;;AAJT,wBAAA,QAAQ,GAAG,EAIF,CAAA,IAAA,EAAA,CAAA;8BAEX,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAA,EAApC,OAAoC,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;8BAEjC,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC,qBAAqB,CAAA,EAAvD,OAAuD,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA;AACtD,wBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;AAC7D,wBAAA,OAAA,CAAA,CAAA,YAAM,SAAS,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA,CAAA;;AAA3C,wBAAA,EAAA,CAAA,IAAA,EAA2C,CAAC;;;;AAG5C,wBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;wBAChE,MAAM,WAAW,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;AAGnE,wBAAA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wDAAwD,CAAC,CAAC;wBAC9E,OAAO,CAAA,CAAA,aAAA,QAAQ,CAAC,IAAI,CAAC,CAAA;;;AAI7B;;;AAGG;AACH,wBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;AAC1D,wBAAA,MAAM,eAAe,CAAC,4BAA4B,EAAE,CAAC;;;;AACxD,KAAA,CAAA;AAED;;;;AAIG;AACK,IAAA,gBAAA,CAAA,SAAA,CAAA,sBAAsB,GAA9B,UAA+B,OAAgC,EAAE,kBAAsC,EAAA;AAEnG,QAAA,IAAM,iBAAiB,GAA4B,IAAI,uBAAuB,EAAE,CAAC;AAEjF,QAAA,iBAAiB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5C,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAChE,QAAA,iBAAiB,CAAC,YAAY,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;AAC5D,QAAA,iBAAiB,CAAC,aAAa,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAC/D,QAAA,IAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,EAAE,CAAC;AAC3F,QAAA,iBAAiB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAClD,iBAAiB,CAAC,aAAa,EAAE,CAAC;QAClC,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAC1D,iBAAiB,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC7E,iBAAiB,CAAC,aAAa,EAAE,CAAC;QAClC,IAAI,IAAI,CAAC,sBAAsB,EAAE;AAC7B,YAAA,iBAAiB,CAAC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACrE,SAAA;AAED,QAAA,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;AAChJ,YAAA,iBAAiB,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAC3F,SAAA;AACD,QAAA,OAAO,iBAAiB,CAAC,iBAAiB,EAAE,CAAC;KAChD,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CA9PA,CAAsC,UAAU,CA8P/C;;;;"}