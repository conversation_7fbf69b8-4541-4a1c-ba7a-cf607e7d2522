{"version": 3, "file": "CacheHelpers.mjs", "sources": ["../../../src/cache/utils/CacheHelpers.ts"], "sourcesContent": [null], "names": ["TimeUtils.nowSeconds", "ClientAuthErrorCodes.tokenClaimsCnfRequiredForSignedJwt"], "mappings": ";;;;;;;;AAAA;;;AAGG;AA2BH;;;;;;;AAOG;AACG,SAAU,qBAAqB,CACjC,gBAAkC,EAAA;AAElC,IAAA,MAAM,aAAa,GAAG;QAClB,iBAAiB,CAAC,gBAAgB,CAAC;QACnC,oBAAoB,CAAC,gBAAgB,CAAC;QACtC,cAAc,CAAC,gBAAgB,CAAC;QAChC,kBAAkB,CAAC,gBAAgB,CAAC;QACpC,cAAc,CAAC,gBAAgB,CAAC;KACnC,CAAC;IAEF,OAAO,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;AAC5E,CAAC;AAED;;;;;;AAMG;AACG,SAAU,mBAAmB,CAC/B,aAAqB,EACrB,WAAmB,EACnB,OAAe,EACf,QAAgB,EAChB,QAAgB,EAAA;AAEhB,IAAA,MAAM,aAAa,GAAkB;QACjC,cAAc,EAAE,cAAc,CAAC,QAAQ;AACvC,QAAA,aAAa,EAAE,aAAa;AAC5B,QAAA,WAAW,EAAE,WAAW;AACxB,QAAA,QAAQ,EAAE,QAAQ;AAClB,QAAA,MAAM,EAAE,OAAO;AACf,QAAA,KAAK,EAAE,QAAQ;KAClB,CAAC;AAEF,IAAA,OAAO,aAAa,CAAC;AACzB,CAAC;AAED;;;;;;;;;;AAUG;AACa,SAAA,uBAAuB,CACnC,aAAqB,EACrB,WAAmB,EACnB,WAAmB,EACnB,QAAgB,EAChB,QAAgB,EAChB,MAAc,EACd,SAAiB,EACjB,YAAoB,EACpB,YAAuC,EACvC,SAAkB,EAClB,SAAgC,EAChC,iBAA0B,EAC1B,KAAc,EACd,eAAwB,EACxB,mBAA4B,EAAA;AAE5B,IAAA,MAAM,QAAQ,GAAsB;AAChC,QAAA,aAAa,EAAE,aAAa;QAC5B,cAAc,EAAE,cAAc,CAAC,YAAY;AAC3C,QAAA,MAAM,EAAE,WAAW;AACnB,QAAA,QAAQ,EAAEA,UAAoB,EAAE,CAAC,QAAQ,EAAE;AAC3C,QAAA,SAAS,EAAE,SAAS,CAAC,QAAQ,EAAE;AAC/B,QAAA,iBAAiB,EAAE,YAAY,CAAC,QAAQ,EAAE;AAC1C,QAAA,WAAW,EAAE,WAAW;AACxB,QAAA,QAAQ,EAAE,QAAQ;AAClB,QAAA,KAAK,EAAE,QAAQ;AACf,QAAA,MAAM,EAAE,MAAM;AACd,QAAA,SAAS,EAAE,SAAS,IAAI,oBAAoB,CAAC,MAAM;KACtD,CAAC;AAEF,IAAA,IAAI,iBAAiB,EAAE;AACnB,QAAA,QAAQ,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAClD,KAAA;AAED,IAAA,IAAI,SAAS,EAAE;AACX,QAAA,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC7C,KAAA;AAED,IAAA,IAAI,eAAe,EAAE;AACjB,QAAA,QAAQ,CAAC,eAAe,GAAG,eAAe,CAAC;AAC3C,QAAA,QAAQ,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AACtD,KAAA;AAED;;;AAGG;AACH,IAAA,IACI,QAAQ,CAAC,SAAS,EAAE,WAAW,EAAE;AACjC,QAAA,oBAAoB,CAAC,MAAM,CAAC,WAAW,EAAE,EAC3C;AACE,QAAA,QAAQ,CAAC,cAAc,GAAG,cAAc,CAAC,6BAA6B,CAAC;QACvE,QAAQ,QAAQ,CAAC,SAAS;YACtB,KAAK,oBAAoB,CAAC,GAAG;;gBAEzB,MAAM,WAAW,GAAuB,kBAAkB,CACtD,WAAW,EACX,YAAY,CACf,CAAC;AACF,gBAAA,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE;AACxB,oBAAA,MAAM,qBAAqB,CACvBC,kCAAuD,CAC1D,CAAC;AACL,iBAAA;gBACD,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC;gBACrC,MAAM;YACV,KAAK,oBAAoB,CAAC,GAAG;AACzB,gBAAA,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;AAC9B,SAAA;AACJ,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED;;;;;;AAMG;AACa,SAAA,wBAAwB,CACpC,aAAqB,EACrB,WAAmB,EACnB,YAAoB,EACpB,QAAgB,EAChB,QAAiB,EACjB,iBAA0B,EAC1B,SAAkB,EAAA;AAElB,IAAA,MAAM,QAAQ,GAAuB;QACjC,cAAc,EAAE,cAAc,CAAC,aAAa;AAC5C,QAAA,aAAa,EAAE,aAAa;AAC5B,QAAA,WAAW,EAAE,WAAW;AACxB,QAAA,QAAQ,EAAE,QAAQ;AAClB,QAAA,MAAM,EAAE,YAAY;KACvB,CAAC;AAEF,IAAA,IAAI,iBAAiB,EAAE;AACnB,QAAA,QAAQ,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAClD,KAAA;AAED,IAAA,IAAI,QAAQ,EAAE;AACV,QAAA,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAChC,KAAA;AAED,IAAA,IAAI,SAAS,EAAE;AACX,QAAA,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC7C,KAAA;AAED,IAAA,OAAO,QAAQ,CAAC;AACpB,CAAC;AAEK,SAAU,kBAAkB,CAAC,MAAc,EAAA;AAC7C,IAAA,QACI,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC;AACtC,QAAA,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC;AACpC,QAAA,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACvC,QAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AACjC,QAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,EACjC;AACN,CAAC;AAED;;;AAGG;AACG,SAAU,mBAAmB,CAAC,MAAc,EAAA;IAC9C,IAAI,CAAC,MAAM,EAAE;AACT,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;AAED,IAAA,QACI,kBAAkB,CAAC,MAAM,CAAC;AAC1B,QAAA,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;AAC9B,QAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC/B,SAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,cAAc,CAAC,YAAY;YACrD,MAAM,CAAC,gBAAgB,CAAC;AACpB,gBAAA,cAAc,CAAC,6BAA6B,CAAC,EACvD;AACN,CAAC;AAED;;;AAGG;AACG,SAAU,eAAe,CAAC,MAAc,EAAA;IAC1C,IAAI,CAAC,MAAM,EAAE;AACT,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;AAED,IAAA,QACI,kBAAkB,CAAC,MAAM,CAAC;AAC1B,QAAA,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;QAC9B,MAAM,CAAC,gBAAgB,CAAC,KAAK,cAAc,CAAC,QAAQ,EACtD;AACN,CAAC;AAED;;;AAGG;AACG,SAAU,oBAAoB,CAAC,MAAc,EAAA;IAC/C,IAAI,CAAC,MAAM,EAAE;AACT,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;AAED,IAAA,QACI,kBAAkB,CAAC,MAAM,CAAC;QAC1B,MAAM,CAAC,gBAAgB,CAAC,KAAK,cAAc,CAAC,aAAa,EAC3D;AACN,CAAC;AAED;;AAEG;AACH,SAAS,iBAAiB,CAAC,gBAAkC,EAAA;AACzD,IAAA,MAAM,SAAS,GAAkB;AAC7B,QAAA,gBAAgB,CAAC,aAAa;AAC9B,QAAA,gBAAgB,CAAC,WAAW;KAC/B,CAAC;IACF,OAAO,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;AACxE,CAAC;AAED;;AAEG;AACH,SAAS,oBAAoB,CAAC,gBAAkC,EAAA;IAC5D,MAAM,gBAAgB,GAClB,gBAAgB,CAAC,cAAc,KAAK,cAAc,CAAC,aAAa;AAC5D,UAAE,gBAAgB,CAAC,QAAQ,IAAI,gBAAgB,CAAC,QAAQ;AACxD,UAAE,gBAAgB,CAAC,QAAQ,CAAC;AACpC,IAAA,MAAM,YAAY,GAAkB;AAChC,QAAA,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB;QAChB,gBAAgB,CAAC,KAAK,IAAI,EAAE;KAC/B,CAAC;IAEF,OAAO,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;AAC3E,CAAC;AAED;;AAEG;AACH,SAAS,cAAc,CAAC,gBAAkC,EAAA;IACtD,OAAO,CAAC,gBAAgB,CAAC,MAAM,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC;AACzD,CAAC;AAED;;AAEG;AACH,SAAS,kBAAkB,CAAC,gBAAkC,EAAA;IAC1D,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC;AACtE,CAAC;AAED;;AAEG;AACH,SAAS,cAAc,CAAC,gBAAkC,EAAA;AACtD;;;AAGG;IACH,OAAO,gBAAgB,CAAC,SAAS;AAC7B,QAAA,gBAAgB,CAAC,SAAS,CAAC,WAAW,EAAE;AACpC,YAAA,oBAAoB,CAAC,MAAM,CAAC,WAAW,EAAE;AAC7C,UAAE,gBAAgB,CAAC,SAAS,CAAC,WAAW,EAAE;UACxC,EAAE,CAAC;AACb,CAAC;AAED;;;;AAIG;AACa,SAAA,uBAAuB,CAAC,GAAW,EAAE,MAAe,EAAA;AAChE,IAAA,MAAM,WAAW,GACb,GAAG,CAAC,OAAO,CAAC,sBAAsB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IACxD,IAAI,cAAc,GAAY,IAAI,CAAC;AAEnC,IAAA,IAAI,MAAM,EAAE;QACR,cAAc;AACV,YAAA,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACvC,gBAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC/B,gBAAA,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AAC1C,KAAA;IAED,OAAO,WAAW,IAAI,cAAc,CAAC;AACzC,CAAC;AAED;;;;AAIG;AACa,SAAA,kBAAkB,CAAC,GAAW,EAAE,MAAe,EAAA;IAC3D,IAAI,WAAW,GAAY,KAAK,CAAC;AACjC,IAAA,IAAI,GAAG,EAAE;QACL,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC1E,KAAA;IAED,IAAI,cAAc,GAAY,IAAI,CAAC;AACnC,IAAA,IAAI,MAAM,EAAE;AACR,QAAA,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC1D,KAAA;IAED,OAAO,WAAW,IAAI,cAAc,CAAC;AACzC,CAAC;AAED;;AAEG;SACa,sBAAsB,CAAC,EACnC,WAAW,EACX,QAAQ,GACQ,EAAA;AAChB,IAAA,MAAM,mBAAmB,GAAkB;QACvC,YAAY;QACZ,WAAW;QACX,QAAQ;KACX,CAAC;AACF,IAAA,OAAO,mBAAmB;AACrB,SAAA,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC;AACpC,SAAA,WAAW,EAAE,CAAC;AACvB,CAAC;AAED;;;AAGG;AACa,SAAA,mBAAmB,CAAC,GAAW,EAAE,MAAc,EAAA;IAC3D,IAAI,CAAC,MAAM,EAAE;AACT,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;IAED,QACI,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC;AAC/B,QAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AACjC,QAAA,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC,EACtC;AACN,CAAC;AAED;;;AAGG;AACa,SAAA,yBAAyB,CACrC,GAAW,EACX,MAAc,EAAA;IAEd,IAAI,CAAC,MAAM,EAAE;AACT,QAAA,OAAO,KAAK,CAAC;AAChB,KAAA;IAED,QACI,GAAG,CAAC,OAAO,CAAC,4BAA4B,CAAC,SAAS,CAAC,KAAK,CAAC;AACzD,QAAA,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC;AAChC,QAAA,MAAM,CAAC,cAAc,CAAC,iBAAiB,CAAC;AACxC,QAAA,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC;AAC1C,QAAA,MAAM,CAAC,cAAc,CAAC,qBAAqB,CAAC;AAC5C,QAAA,MAAM,CAAC,cAAc,CAAC,wBAAwB,CAAC;AAC/C,QAAA,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC;AACvC,QAAA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC/B,QAAA,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC;AAC3C,QAAA,MAAM,CAAC,cAAc,CAAC,sBAAsB,CAAC;AAC7C,QAAA,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC;AAClC,QAAA,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,EACnC;AACN,CAAC;AAED;;AAEG;SACa,kCAAkC,GAAA;AAC9C,IAAA,QACID,UAAoB,EAAE;QACtB,4BAA4B,CAAC,oBAAoB,EACnD;AACN,CAAC;SAEe,+BAA+B,CAC3C,iBAA0C,EAC1C,aAAmC,EACnC,WAAoB,EAAA;AAEpB,IAAA,iBAAiB,CAAC,sBAAsB;QACpC,aAAa,CAAC,sBAAsB,CAAC;AACzC,IAAA,iBAAiB,CAAC,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC;AAChE,IAAA,iBAAiB,CAAC,oBAAoB,GAAG,aAAa,CAAC,oBAAoB,CAAC;AAC5E,IAAA,iBAAiB,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;AAChD,IAAA,iBAAiB,CAAC,oBAAoB,GAAG,WAAW,CAAC;AACrD,IAAA,iBAAiB,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC;AACxD,CAAC;SAEe,4BAA4B,CACxC,iBAA0C,EAC1C,aAAqC,EACrC,WAAoB,EAAA;AAEpB,IAAA,iBAAiB,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;AAClD,IAAA,iBAAiB,CAAC,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC;AAClE,IAAA,iBAAiB,CAAC,iBAAiB,GAAG,aAAa,CAAC,iBAAiB,CAAC;AACtE,IAAA,iBAAiB,CAAC,kBAAkB,GAAG,WAAW,CAAC;AACvD,CAAC;AAED;;AAEG;AACG,SAAU,0BAA0B,CACtC,QAAiC,EAAA;IAEjC,OAAO,QAAQ,CAAC,SAAS,IAAIA,UAAoB,EAAE,CAAC;AACxD;;;;"}