{"version": 3, "file": "ICacheManager.d.ts", "sourceRoot": "", "sources": ["../../../../src/cache/interface/ICacheManager.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAC7D,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AACrE,OAAO,EAAE,qBAAqB,EAAE,MAAM,sCAAsC,CAAC;AAC7E,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAC7D,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AACrE,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,uBAAuB,EAAE,MAAM,wCAAwC,CAAC;AACjF,OAAO,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAE7D,MAAM,WAAW,aAAa;IAC1B;;;OAGG;IACH,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC;IAE5E;;;OAGG;IACH,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEzE;;;;;;OAMG;IACH,YAAY,CACR,GAAG,EAAE,MAAM,EACX,aAAa,CAAC,EAAE,MAAM,EACtB,QAAQ,CAAC,EAAE,MAAM,GAClB,OAAO,CAAC;IAEX;;;OAGG;IACH,oBAAoB,CAChB,UAAU,EAAE,MAAM,EAClB,aAAa,EAAE,MAAM,GACtB,aAAa,GAAG,IAAI,CAAC;IAExB;;;OAGG;IACH,oBAAoB,CAChB,OAAO,EAAE,aAAa,EACtB,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB;;;OAGG;IACH,wBAAwB,CACpB,cAAc,EAAE,MAAM,EACtB,aAAa,EAAE,MAAM,GACtB,iBAAiB,GAAG,IAAI,CAAC;IAE5B;;;OAGG;IACH,wBAAwB,CACpB,WAAW,EAAE,iBAAiB,EAC9B,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB;;;OAGG;IACH,yBAAyB,CACrB,eAAe,EAAE,MAAM,EACvB,aAAa,EAAE,MAAM,GACtB,kBAAkB,GAAG,IAAI,CAAC;IAE7B;;;OAGG;IACH,yBAAyB,CACrB,YAAY,EAAE,kBAAkB,EAChC,aAAa,EAAE,MAAM,GACtB,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB;;;OAGG;IACH,cAAc,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI,CAAC;IAEjE;;;OAGG;IACH,cAAc,CAAC,WAAW,EAAE,iBAAiB,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5E;;;OAGG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,GAC3B,qBAAqB,GAAG,IAAI,CAAC;IAEhC;;;;OAIG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,qBAAqB,EACtC,aAAa,EAAE,MAAM,GACtB,IAAI,CAAC;IAER;;;OAGG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI,CAAC;IAElE;;OAEG;IACH,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1C;;;;OAIG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,uBAAuB,GAAG,IAAI,CAAC;IAExE;;;OAGG;IACH,2BAA2B,CAAC,IAAI,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI,CAAC;IAE1E;;;OAGG;IACH,iCAAiC,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,CAAC;IAE7D;;;OAGG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI,CAAC;IAExE;;;;OAIG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,gBAAgB,EACjC,aAAa,EAAE,MAAM,GACtB,IAAI,CAAC;IAER;;OAEG;IACH,cAAc,CACV,aAAa,EAAE,aAAa,EAC5B,aAAa,EAAE,MAAM,GACtB,WAAW,EAAE,CAAC;IAEjB;;;OAGG;IACH,eAAe,CACX,WAAW,EAAE,WAAW,EACxB,aAAa,EAAE,MAAM,EACrB,YAAY,CAAC,EAAE,YAAY,GAC5B,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB;;;;;OAKG;IACH,qBAAqB,CACjB,MAAM,EAAE,aAAa,EACrB,aAAa,EAAE,MAAM,GACtB,aAAa,EAAE,CAAC;IAEnB;;;OAGG;IACH,wBAAwB,CACpB,MAAM,EAAE,aAAa,EACrB,aAAa,EAAE,MAAM,GACtB,WAAW,GAAG,IAAI,CAAC;IAEtB;;OAEG;IACH,iBAAiB,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/C;;;OAGG;IACH,aAAa,CAAC,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/D;;;OAGG;IACH,oBAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAE1E;;OAEG;IACH,aAAa,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAExD;;OAEG;IACH,iBAAiB,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5D;;OAEG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;CAChE"}