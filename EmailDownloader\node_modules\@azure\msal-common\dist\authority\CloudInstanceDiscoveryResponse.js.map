{"version": 3, "file": "CloudInstanceDiscoveryResponse.js", "sources": ["../../src/authority/CloudInstanceDiscoveryResponse.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { CloudDiscoveryMetadata } from \"./CloudDiscoveryMetadata\";\r\n\r\n/**\r\n * The OpenID Configuration Endpoint Response type. Used by the authority class to get relevant OAuth endpoints.\r\n */\r\nexport type CloudInstanceDiscoveryResponse = {\r\n    tenant_discovery_endpoint: string;\r\n    metadata: Array<CloudDiscoveryMetadata>;\r\n};\r\n\r\nexport function isCloudInstanceDiscoveryResponse(response: object): boolean {\r\n    return (\r\n        response.hasOwnProperty(\"tenant_discovery_endpoint\") &&\r\n        response.hasOwnProperty(\"metadata\")\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAYG,SAAU,gCAAgC,CAAC,QAAgB,EAAA;AAC7D,IAAA,QACI,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAC;AACpD,QAAA,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,EACrC;AACN;;;;"}