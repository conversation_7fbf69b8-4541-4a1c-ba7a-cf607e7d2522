{"version": 3, "file": "RequestValidator.js", "sources": ["../../src/request/RequestValidator.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { StringUtils } from \"../utils/StringUtils\";\r\nimport { ClientConfigurationError } from \"../error/ClientConfigurationError\";\r\nimport { PromptValue, CodeChallengeMethodValues} from \"../utils/Constants\";\r\nimport { StringDict } from \"../utils/MsalTypes\";\r\n\r\n/**\r\n * Validates server consumable params from the \"request\" objects\r\n */\r\nexport class RequestValidator {\r\n\r\n    /**\r\n     * Utility to check if the `redirectUri` in the request is a non-null value\r\n     * @param redirectUri\r\n     */\r\n    static validateRedirectUri(redirectUri: string) : void {\r\n        if (StringUtils.isEmpty(redirectUri)) {\r\n            throw ClientConfigurationError.createRedirectUriEmptyError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Utility to validate prompt sent by the user in the request\r\n     * @param prompt\r\n     */\r\n    static validatePrompt(prompt: string) : void {\r\n        const promptValues = [];\r\n\r\n        for (const value in PromptValue) {\r\n            promptValues.push(PromptValue[value]);\r\n        }\r\n\r\n        if (promptValues.indexOf(prompt) < 0) {\r\n            throw ClientConfigurationError.createInvalidPromptError(prompt);\r\n        }\r\n    }\r\n\r\n    static validateClaims(claims: string) : void {\r\n        try {\r\n            JSON.parse(claims);\r\n        } catch(e) {\r\n            throw ClientConfigurationError.createInvalidClaimsRequestError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Utility to validate code_challenge and code_challenge_method\r\n     * @param codeChallenge\r\n     * @param codeChallengeMethod\r\n     */\r\n    static validateCodeChallengeParams(codeChallenge: string, codeChallengeMethod: string) : void  {\r\n        if (StringUtils.isEmpty(codeChallenge) || StringUtils.isEmpty(codeChallengeMethod)) {\r\n            throw ClientConfigurationError.createInvalidCodeChallengeParamsError();\r\n        } else {\r\n            this.validateCodeChallengeMethod(codeChallengeMethod);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Utility to validate code_challenge_method\r\n     * @param codeChallengeMethod\r\n     */\r\n    static validateCodeChallengeMethod(codeChallengeMethod: string) : void {\r\n        if (\r\n            [\r\n                CodeChallengeMethodValues.PLAIN,\r\n                CodeChallengeMethodValues.S256\r\n            ].indexOf(codeChallengeMethod) < 0\r\n        ) {\r\n            throw ClientConfigurationError.createInvalidCodeChallengeMethodError();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Removes unnecessary, duplicate, and empty string query parameters from extraQueryParameters\r\n     * @param request\r\n     */\r\n    static sanitizeEQParams(eQParams: StringDict, queryParams: Map<string, string>) : StringDict {\r\n        if (!eQParams) {\r\n            return {};\r\n        }\r\n\r\n        // Remove any query parameters already included in SSO params\r\n        queryParams.forEach((value, key) => {\r\n            if (eQParams[key]) {\r\n                delete eQParams[key];\r\n            }\r\n        });\r\n\r\n        // remove empty string parameters\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        return Object.fromEntries(Object.entries(eQParams).filter(([key, value]) => value !== \"\"));\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAGG;AAOH;;AAEG;AACH,IAAA,gBAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,gBAAA,GAAA;KAoFC;AAlFG;;;AAGG;IACI,gBAAmB,CAAA,mBAAA,GAA1B,UAA2B,WAAmB,EAAA;AAC1C,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAClC,YAAA,MAAM,wBAAwB,CAAC,2BAA2B,EAAE,CAAC;AAChE,SAAA;KACJ,CAAA;AAED;;;AAGG;IACI,gBAAc,CAAA,cAAA,GAArB,UAAsB,MAAc,EAAA;QAChC,IAAM,YAAY,GAAG,EAAE,CAAC;AAExB,QAAA,KAAK,IAAM,KAAK,IAAI,WAAW,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC,SAAA;QAED,IAAI,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AAClC,YAAA,MAAM,wBAAwB,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;AACnE,SAAA;KACJ,CAAA;IAEM,gBAAc,CAAA,cAAA,GAArB,UAAsB,MAAc,EAAA;QAChC,IAAI;AACA,YAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACtB,SAAA;AAAC,QAAA,OAAM,CAAC,EAAE;AACP,YAAA,MAAM,wBAAwB,CAAC,+BAA+B,EAAE,CAAC;AACpE,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACI,IAAA,gBAAA,CAAA,2BAA2B,GAAlC,UAAmC,aAAqB,EAAE,mBAA2B,EAAA;AACjF,QAAA,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;AAChF,YAAA,MAAM,wBAAwB,CAAC,qCAAqC,EAAE,CAAC;AAC1E,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,CAAC;AACzD,SAAA;KACJ,CAAA;AAED;;;AAGG;IACI,gBAA2B,CAAA,2BAAA,GAAlC,UAAmC,mBAA2B,EAAA;QAC1D,IACI;AACI,YAAA,yBAAyB,CAAC,KAAK;AAC/B,YAAA,yBAAyB,CAAC,IAAI;AACjC,SAAA,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,EACpC;AACE,YAAA,MAAM,wBAAwB,CAAC,qCAAqC,EAAE,CAAC;AAC1E,SAAA;KACJ,CAAA;AAED;;;AAGG;AACI,IAAA,gBAAA,CAAA,gBAAgB,GAAvB,UAAwB,QAAoB,EAAE,WAAgC,EAAA;QAC1E,IAAI,CAAC,QAAQ,EAAE;AACX,YAAA,OAAO,EAAE,CAAC;AACb,SAAA;;AAGD,QAAA,WAAW,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG,EAAA;AAC3B,YAAA,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;AACf,gBAAA,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;AACxB,aAAA;AACL,SAAC,CAAC,CAAC;;;AAIH,QAAA,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAC,EAAY,EAAA;gBAAN,KAAK,GAAA,EAAA,CAAA,CAAA,EAAA;YAAM,OAAA,KAAK,KAAK,EAAE,CAAA;SAAA,CAAC,CAAC,CAAC;KAC9F,CAAA;IACL,OAAC,gBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}