{"version": 3, "file": "UrlString.mjs", "sources": ["../../src/url/UrlString.ts"], "sourcesContent": [null], "names": ["ClientConfigurationErrorCodes.urlEmptyError", "ClientConfigurationErrorCodes.urlParseError", "ClientConfigurationErrorCodes.authorityUriInsecure", "UrlUtils.getDeserializedResponse"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAWH;;AAEG;MACU,SAAS,CAAA;AAGlB,IAAA,IAAW,SAAS,GAAA;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;KAC1B;AAED,IAAA,WAAA,CAAY,GAAW,EAAA;AACnB,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;;AAElB,YAAA,MAAM,8BAA8B,CAChCA,aAA2C,CAC9C,CAAC;AACL,SAAA;AAED,QAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACpB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;AACpD,SAAA;KACJ;AAED;;;AAGG;IACH,OAAO,eAAe,CAAC,GAAW,EAAA;AAC9B,QAAA,IAAI,GAAG,EAAE;AACL,YAAA,IAAI,YAAY,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;YAErC,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE;gBACzC,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC5C,aAAA;iBAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE;gBACjD,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC5C,aAAA;YAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE;gBAC1C,YAAY,IAAI,GAAG,CAAC;AACvB,aAAA;AAED,YAAA,OAAO,YAAY,CAAC;AACvB,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;KACd;AAED;;AAEG;IACH,aAAa,GAAA;;AAET,QAAA,IAAI,UAAU,CAAC;QACf,IAAI;AACA,YAAA,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxC,SAAA;AAAC,QAAA,OAAO,CAAC,EAAE;AACR,YAAA,MAAM,8BAA8B,CAChCC,aAA2C,CAC9C,CAAC;AACL,SAAA;;QAGD,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;AACzD,YAAA,MAAM,8BAA8B,CAChCA,aAA2C,CAC9C,CAAC;AACL,SAAA;;QAGD,IACI,CAAC,UAAU,CAAC,QAAQ;AACpB,YAAA,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,EAChD;AACE,YAAA,MAAM,8BAA8B,CAChCC,oBAAkD,CACrD,CAAC;AACL,SAAA;KACJ;AAED;;;;AAIG;AACH,IAAA,OAAO,iBAAiB,CAAC,GAAW,EAAE,WAAmB,EAAA;QACrD,IAAI,CAAC,WAAW,EAAE;AACd,YAAA,OAAO,GAAG,CAAC;AACd,SAAA;AAED,QAAA,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;AACvB,cAAE,CAAA,EAAG,GAAG,CAAA,CAAA,EAAI,WAAW,CAAE,CAAA;AACzB,cAAE,CAAG,EAAA,GAAG,CAAI,CAAA,EAAA,WAAW,EAAE,CAAC;KACjC;AAED;;;AAGG;IACH,OAAO,iBAAiB,CAAC,GAAW,EAAA;AAChC,QAAA,OAAO,SAAS,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvD;AAED;;;;AAIG;AACH,IAAA,iBAAiB,CAAC,QAAgB,EAAA;AAC9B,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC1C,QAAA,MAAM,SAAS,GAAG,SAAS,CAAC,YAAY,CAAC;AACzC,QAAA,IACI,QAAQ;YACR,SAAS,CAAC,MAAM,KAAK,CAAC;AACtB,aAAC,SAAS,CAAC,CAAC,CAAC,KAAK,qBAAqB,CAAC,MAAM;gBAC1C,SAAS,CAAC,CAAC,CAAC,KAAK,qBAAqB,CAAC,aAAa,CAAC,EAC3D;AACE,YAAA,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC;AAC3B,SAAA;AACD,QAAA,OAAO,SAAS,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;KAC/D;AAED;;;AAGG;IACH,gBAAgB,GAAA;;AAEZ,QAAA,MAAM,KAAK,GAAG,MAAM,CAChB,4DAA4D,CAC/D,CAAC;;QAGF,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,MAAM,8BAA8B,CAChCD,aAA2C,CAC9C,CAAC;AACL,SAAA;;AAGD,QAAA,MAAM,aAAa,GAAG;AAClB,YAAA,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;AAClB,YAAA,eAAe,EAAE,KAAK,CAAC,CAAC,CAAC;AACzB,YAAA,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;AACtB,YAAA,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;SAChB,CAAC;QAEV,IAAI,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzD,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACnE,QAAA,aAAa,CAAC,YAAY,GAAG,YAAY,CAAC;QAE1C,IACI,aAAa,CAAC,WAAW;AACzB,YAAA,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EACzC;AACE,YAAA,aAAa,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC,SAAS,CAC3D,CAAC,EACD,aAAa,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CACvC,CAAC;AACL,SAAA;AACD,QAAA,OAAO,aAAa,CAAC;KACxB;IAED,OAAO,gBAAgB,CAAC,GAAW,EAAA;AAC/B,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,0BAA0B,CAAC,CAAC;QAEjD,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAE/B,IAAI,CAAC,KAAK,EAAE;AACR,YAAA,MAAM,8BAA8B,CAChCA,aAA2C,CAC9C,CAAC;AACL,SAAA;AAED,QAAA,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;KACnB;AAED,IAAA,OAAO,cAAc,CAAC,WAAmB,EAAE,OAAe,EAAA;QACtD,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,aAAa,EAAE;AAC5C,YAAA,MAAM,GAAG,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC;AACnC,YAAA,MAAM,cAAc,GAAG,GAAG,CAAC,gBAAgB,EAAE,CAAC;YAE9C,QACI,cAAc,CAAC,QAAQ;gBACvB,IAAI;AACJ,gBAAA,cAAc,CAAC,eAAe;AAC9B,gBAAA,WAAW,EACb;AACL,SAAA;AAED,QAAA,OAAO,WAAW,CAAC;KACtB;IAED,OAAO,+BAA+B,CAAC,SAAe,EAAA;AAClD,QAAA,OAAO,IAAI,SAAS,CAChB,SAAS,CAAC,QAAQ;YACd,IAAI;AACJ,YAAA,SAAS,CAAC,eAAe;YACzB,GAAG;YACH,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CACvC,CAAC;KACL;AAED;;;AAGG;IACH,OAAO,2BAA2B,CAAC,QAAgB,EAAA;QAC/C,OAAO,CAAC,CAACE,uBAAgC,CAAC,QAAQ,CAAC,CAAC;KACvD;AACJ;;;;"}