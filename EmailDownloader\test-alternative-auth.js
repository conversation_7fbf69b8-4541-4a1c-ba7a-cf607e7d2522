// Test alternative authentication approaches
require('dotenv').config();
const msal = require('@azure/msal-node');
const axios = require('axios');

async function testAlternativeAuth() {
    console.log("Testing alternative authentication approaches...\n");
    
    const msalConfig = {
        auth: {
            clientId: process.env.CLIENT_ID,
            authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
            clientSecret: process.env.CLIENT_SECRET
        }
    };
    
    const cca = new msal.ConfidentialClientApplication(msalConfig);
    
    // Test 1: Try with Exchange Online scope
    console.log("Test 1: Trying with Exchange Online scope...");
    try {
        const tokenRequest = { scopes: ['https://outlook.office365.com/.default'] };
        const response = await cca.acquireTokenByClientCredential(tokenRequest);
        console.log("✅ Got token for Exchange Online scope");
        
        const api = axios.create({
            baseURL: 'https://outlook.office365.com/api/v2.0',
            headers: { Authorization: `Bearer ${response.accessToken}` }
        });
        
        try {
            const response = await api.get(`/users/${process.env.MAIL_USER_ID}/mailfolders`);
            console.log(`✅ Exchange API worked! Found ${response.data.value?.length || 0} folders`);
        } catch (error) {
            console.log(`❌ Exchange API failed: ${error.response?.data?.error?.message || error.message}`);
        }
        
    } catch (error) {
        console.log(`❌ Exchange Online scope failed: ${error.message}`);
    }
    
    // Test 2: Try with custom API scope
    console.log("\nTest 2: Trying with your custom API scope...");
    try {
        const tokenRequest = { scopes: ['api://64590f6c-0924-4dbd-b1b4-af5b6f3bb123/.default'] };
        const response = await cca.acquireTokenByClientCredential(tokenRequest);
        console.log("✅ Got token for custom API scope");
        console.log(`   Token audience: ${JSON.parse(Buffer.from(response.accessToken.split('.')[1], 'base64')).aud}`);
        
        // Try to use this token with Microsoft Graph anyway
        const api = axios.create({
            baseURL: 'https://graph.microsoft.com/v1.0',
            headers: { Authorization: `Bearer ${response.accessToken}` }
        });
        
        try {
            const response = await api.get(`/users/${process.env.MAIL_USER_ID}/mailfolders`);
            console.log(`✅ Custom scope with Graph API worked! Found ${response.data.value?.length || 0} folders`);
        } catch (error) {
            console.log(`❌ Custom scope with Graph API failed: ${error.response?.data?.error?.message || error.message}`);
        }
        
    } catch (error) {
        console.log(`❌ Custom API scope failed: ${error.message}`);
    }
    
    // Test 3: Check what permissions are actually available
    console.log("\nTest 3: Analyzing available permissions...");
    try {
        const tokenRequest = { scopes: ['https://graph.microsoft.com/.default'] };
        const response = await cca.acquireTokenByClientCredential(tokenRequest);
        
        // Decode token to see what's in it
        const tokenPayload = JSON.parse(Buffer.from(response.accessToken.split('.')[1], 'base64'));
        
        console.log("Token details:");
        console.log(`   Audience: ${tokenPayload.aud}`);
        console.log(`   App ID: ${tokenPayload.appid}`);
        console.log(`   Tenant: ${tokenPayload.tid}`);
        
        if (tokenPayload.roles && tokenPayload.roles.length > 0) {
            console.log("   Application Roles:");
            tokenPayload.roles.forEach(role => console.log(`     - ${role}`));
        } else {
            console.log("   ❌ No application roles found");
        }
        
        if (tokenPayload.scp) {
            console.log(`   Scopes: ${tokenPayload.scp}`);
        }
        
        // Test if we can at least get user info
        const api = axios.create({
            baseURL: 'https://graph.microsoft.com/v1.0',
            headers: { Authorization: `Bearer ${response.accessToken}` }
        });
        
        console.log("\nTesting basic Graph API calls:");
        
        // Try to get organization info (might work without specific permissions)
        try {
            const orgResponse = await api.get('/organization');
            console.log(`✅ Organization info accessible: ${orgResponse.data.value[0]?.displayName || 'Unknown'}`);
        } catch (error) {
            console.log(`❌ Organization info failed: ${error.response?.data?.error?.message || error.message}`);
        }
        
        // Try to get applications (might work)
        try {
            const appsResponse = await api.get('/applications?$top=1');
            console.log(`✅ Applications endpoint accessible`);
        } catch (error) {
            console.log(`❌ Applications endpoint failed: ${error.response?.data?.error?.message || error.message}`);
        }
        
    } catch (error) {
        console.log(`❌ Token analysis failed: ${error.message}`);
    }
    
    console.log("\n" + "=".repeat(60));
    console.log("CONCLUSION:");
    console.log("Based on these tests, you need admin consent for Microsoft Graph permissions.");
    console.log("Contact your IT administrator to grant consent for Mail.Read and User.Read.All.");
    console.log("=".repeat(60));
}

testAlternativeAuth().catch(console.error);
