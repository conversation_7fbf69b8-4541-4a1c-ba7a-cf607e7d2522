# PowerShell script to check and configure Azure App permissions
# Run this script as an administrator in your Azure tenant

# Variables from your .env file
$TenantId = "2e765a21-6c42-4a30-8c32-c3eeae810ee1"
$ClientId = "64590f6c-0924-4dbd-b1b4-af5b6f3bb123"

Write-Host "Checking Azure App Registration permissions..." -ForegroundColor Yellow
Write-Host "Tenant ID: $TenantId" -ForegroundColor Cyan
Write-Host "Client ID: $ClientId" -ForegroundColor Cyan

# Check if Microsoft Graph PowerShell module is installed
if (!(Get-Module -ListAvailable -Name Microsoft.Graph)) {
    Write-Host "Microsoft Graph PowerShell module not found. Installing..." -ForegroundColor Yellow
    Install-Module Microsoft.Graph -Scope CurrentUser -Force
}

# Connect to Microsoft Graph
Write-Host "Connecting to Microsoft Graph..." -ForegroundColor Yellow
Connect-MgGraph -TenantId $TenantId -Scopes "Application.ReadWrite.All", "AppRoleAssignment.ReadWrite.All"

try {
    # Get the service principal for your app
    $servicePrincipal = Get-MgServicePrincipal -Filter "AppId eq '$ClientId'"
    
    if ($servicePrincipal) {
        Write-Host "Found service principal: $($servicePrincipal.DisplayName)" -ForegroundColor Green
        
        # Get Microsoft Graph service principal
        $graphServicePrincipal = Get-MgServicePrincipal -Filter "AppId eq '00000003-0000-0000-c000-000000000000'"
        
        # Get current app role assignments
        $currentAssignments = Get-MgServicePrincipalAppRoleAssignment -ServicePrincipalId $servicePrincipal.Id
        
        Write-Host "`nCurrent Microsoft Graph permissions:" -ForegroundColor Yellow
        foreach ($assignment in $currentAssignments) {
            if ($assignment.ResourceId -eq $graphServicePrincipal.Id) {
                $appRole = $graphServicePrincipal.AppRoles | Where-Object { $_.Id -eq $assignment.AppRoleId }
                Write-Host "  - $($appRole.Value): $($appRole.DisplayName)" -ForegroundColor Cyan
            }
        }
        
        # Check for required permissions
        $requiredPermissions = @("Mail.Read", "Mail.ReadWrite")
        $missingPermissions = @()
        
        foreach ($permission in $requiredPermissions) {
            $hasPermission = $false
            foreach ($assignment in $currentAssignments) {
                if ($assignment.ResourceId -eq $graphServicePrincipal.Id) {
                    $appRole = $graphServicePrincipal.AppRoles | Where-Object { $_.Id -eq $assignment.AppRoleId }
                    if ($appRole.Value -eq $permission) {
                        $hasPermission = $true
                        break
                    }
                }
            }
            if (-not $hasPermission) {
                $missingPermissions += $permission
            }
        }
        
        if ($missingPermissions.Count -gt 0) {
            Write-Host "`nMissing required permissions:" -ForegroundColor Red
            foreach ($permission in $missingPermissions) {
                Write-Host "  - $permission" -ForegroundColor Red
            }
            
            Write-Host "`nTo fix this issue:" -ForegroundColor Yellow
            Write-Host "1. Go to Azure Portal: https://portal.azure.com" -ForegroundColor White
            Write-Host "2. Navigate to App registrations > Your App > API permissions" -ForegroundColor White
            Write-Host "3. Add the missing Microsoft Graph Application permissions" -ForegroundColor White
            Write-Host "4. Grant admin consent for your organization" -ForegroundColor White
        } else {
            Write-Host "`nAll required permissions are present!" -ForegroundColor Green
            Write-Host "The issue might be:" -ForegroundColor Yellow
            Write-Host "1. Admin consent not granted" -ForegroundColor White
            Write-Host "2. User mailbox access restrictions" -ForegroundColor White
            Write-Host "3. Conditional access policies" -ForegroundColor White
        }
        
    } else {
        Write-Host "Service principal not found. Make sure the app is registered in this tenant." -ForegroundColor Red
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure you have sufficient permissions to read app registrations." -ForegroundColor Yellow
}

# Disconnect
Disconnect-MgGraph
Write-Host "`nScript completed." -ForegroundColor Green
