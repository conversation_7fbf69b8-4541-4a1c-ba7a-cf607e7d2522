// Email Downloader using User Authentication (No Admin Consent Required)
require('dotenv').config();
const msal = require('@azure/msal-node');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const puppeteer = require('puppeteer');
const http = require('http');
const url = require('url');

// Configuration
const config = {
    auth: {
        clientId: process.env.CLIENT_ID,
        authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
        clientSecret: process.env.CLIENT_SECRET,
        redirectUri: "http://localhost:3000"
    },
    mail: { userId: process.env.MAIL_USER_ID, folderName: process.env.TARGET_FOLDER_NAME },
    savePath: process.env.SAVE_PATH,
};

// Authentication using Authorization Code Flow
async function getAccessToken() {
    return new Promise((resolve, reject) => {
        const msalConfig = {
            auth: {
                clientId: process.env.CLIENT_ID,
                authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
                clientSecret: process.env.CLIENT_SECRET
            }
        };

        const cca = new msal.ConfidentialClientApplication(msalConfig);
        
        // Create a simple HTTP server to handle the redirect
        const server = http.createServer(async (req, res) => {
            const parsedUrl = url.parse(req.url, true);
            
            if (parsedUrl.pathname === '/') {
                // Redirect to Microsoft login
                const authCodeUrlParameters = {
                    scopes: ["https://graph.microsoft.com/Mail.Read", "https://graph.microsoft.com/User.Read"],
                    redirectUri: config.auth.redirectUri,
                };
                
                try {
                    const authUrl = await cca.getAuthCodeUrl(authCodeUrlParameters);
                    res.writeHead(302, { Location: authUrl });
                    res.end();
                } catch (error) {
                    res.writeHead(500);
                    res.end(`Error: ${error.message}`);
                }
            } else if (parsedUrl.query.code) {
                // Handle the callback with authorization code
                const tokenRequest = {
                    code: parsedUrl.query.code,
                    scopes: ["https://graph.microsoft.com/Mail.Read", "https://graph.microsoft.com/User.Read"],
                    redirectUri: config.auth.redirectUri,
                };
                
                try {
                    const response = await cca.acquireTokenByCode(tokenRequest);
                    res.writeHead(200, { 'Content-Type': 'text/html' });
                    res.end(`
                        <html>
                            <body>
                                <h2>✅ Authentication Successful!</h2>
                                <p>You can close this window and return to the terminal.</p>
                                <script>window.close();</script>
                            </body>
                        </html>
                    `);
                    server.close();
                    resolve(response.accessToken);
                } catch (error) {
                    res.writeHead(500);
                    res.end(`Authentication failed: ${error.message}`);
                    server.close();
                    reject(error);
                }
            } else {
                res.writeHead(404);
                res.end('Not found');
            }
        });
        
        server.listen(3000, () => {
            console.log("\n" + "=".repeat(60));
            console.log("USER AUTHENTICATION REQUIRED");
            console.log("=".repeat(60));
            console.log("1. Open your web browser");
            console.log("2. Go to: http://localhost:3000");
            console.log("3. Sign in with your Microsoft account");
            console.log("4. Grant permissions when prompted");
            console.log("5. Return here after authentication completes");
            console.log("=".repeat(60) + "\n");
        });
    });
}

// Helper Functions
function sanitizeFileName(name) {
    if (!name) return "Untitled";
    return name.replace(/[\\/:*?"<>|]/g, '-').trim();
}

async function getFolderId(api, folderName) {
    console.log(`Searching for folder named '${folderName}'...`);
    const filter = `$filter=displayName eq '${folderName}'`;
    const url = `/me/mailFolders?${filter}`;
    const response = await api.get(url);
    if (response.data.value.length > 0) {
        const folderId = response.data.value[0].id;
        console.log(`Found folder ID: ${folderId}`);
        return folderId;
    } else {
        throw new Error(`Mail folder '${folderName}' not found.`);
    }
}

async function saveBodyAsPdf(message, fullFolderPath) {
    const sanitizedSubject = sanitizeFileName(message.subject);
    const pdfPath = path.join(fullFolderPath, `Email - ${sanitizedSubject}.pdf`);
    console.log(`  -> Saving email body as PDF: ${path.basename(pdfPath)}`);
    const browser = await puppeteer.launch({ args: ['--no-sandbox', '--disable-setuid-sandbox'] });
    const page = await browser.newPage();
    await page.setContent(message.body.content, { waitUntil: 'networkidle0' });
    await page.pdf({ path: pdfPath, format: 'A4', printBackground: true });
    await browser.close();
}

async function saveAttachments(api, message, fullFolderPath) {
    if (!message.hasAttachments) return;
    console.log("  -> Checking for attachments...");
    const url = `/me/messages/${message.id}/attachments`;
    const response = await api.get(url);
    const attachments = response.data.value;
    for (const attachment of attachments) {
        const isImage = /\.(jpg|jpeg|png|gif|bmp|ico)$/i.test(attachment.name);
        const minImageSize = 102400; // 100 KB
        if (isImage && attachment.size < minImageSize) {
            console.log(`     - Skipping small image: ${attachment.name} (${attachment.size} bytes)`);
            continue;
        }
        const attachmentPath = path.join(fullFolderPath, sanitizeFileName(attachment.name));
        console.log(`     - Saving attachment: ${attachment.name}`);
        const contentBytes = Buffer.from(attachment.contentBytes, 'base64');
        await fs.writeFile(attachmentPath, contentBytes);
    }
}

// Main Function
async function main() {
    console.log("Starting email download process with user authentication...");
    try {
        const accessToken = await getAccessToken();
        console.log("✅ Authentication successful!");
        
        const api = axios.create({
            baseURL: 'https://graph.microsoft.com/v1.0',
            headers: { Authorization: `Bearer ${accessToken}` }
        });
        
        const folderId = await getFolderId(api, config.mail.folderName);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const dateFilter = `$filter=receivedDateTime ge ${yesterday.toISOString()} and receivedDateTime lt ${today.toISOString()}`;
        
        console.log("Retrieving emails from yesterday...");
        const messageUrl = `/me/mailFolders/${folderId}/messages?${dateFilter}`;
        const messageResponse = await api.get(messageUrl);
        const messages = messageResponse.data.value;
        
        if (messages.length === 0) {
            console.log("No new emails found from yesterday. Process complete.");
            return;
        }
        
        console.log(`Found ${messages.length} email(s) to process.`);
        for (const message of messages) {
            const subject = message.subject || "No Subject";
            console.log(`\nProcessing email: "${subject}"`);
            const sanitizedSubject = sanitizeFileName(subject);
            const emailFolderPath = path.join(config.savePath, sanitizedSubject);
            await fs.mkdir(emailFolderPath, { recursive: true });
            await saveBodyAsPdf(message, emailFolderPath);
            await saveAttachments(api, message, emailFolderPath);
        }
        console.log("\nProcess finished successfully!");
        
    } catch (error) {
        console.error("\n[PROCESS FAILED]");
        console.error("Error details:", error.message);
    }
}

main();
