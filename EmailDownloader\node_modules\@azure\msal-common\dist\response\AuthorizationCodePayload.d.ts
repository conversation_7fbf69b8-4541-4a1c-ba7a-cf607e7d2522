/**
 * Response returned after processing the code response query string or fragment.
 */
export declare type AuthorizationCodePayload = {
    code: string;
    cloud_instance_name?: string;
    cloud_instance_host_name?: string;
    cloud_graph_host_name?: string;
    msgraph_host?: string;
    state?: string;
    nonce?: string;
    client_info?: string;
};
//# sourceMappingURL=AuthorizationCodePayload.d.ts.map