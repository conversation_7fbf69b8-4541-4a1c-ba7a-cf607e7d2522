{"version": 3, "file": "IPublicClientApplication.d.ts", "sourceRoot": "", "sources": ["../../src/client/IPublicClientApplication.ts"], "names": [], "mappings": "AAKA,OAAO,EACH,WAAW,EACX,oBAAoB,EACpB,MAAM,EACT,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,wBAAwB,EAAE,MAAM,wCAAwC,CAAC;AAClF,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AACxE,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACpD,OAAO,EAAE,kBAAkB,EAAE,MAAM,kCAAkC,CAAC;AACtE,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAE9D;;;GAGG;AACH,MAAM,WAAW,wBAAwB;IACrC,mDAAmD;IACnD,cAAc,CAAC,OAAO,EAAE,uBAAuB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAElE,8HAA8H;IAC9H,kBAAkB,CACd,OAAO,EAAE,wBAAwB,GAClC,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAEjC,qCAAqC;IACrC,uBAAuB,CACnB,OAAO,EAAE,kBAAkB,GAC5B,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAEjC,6FAA6F;IAC7F,kBAAkB,CACd,OAAO,EAAE,iBAAiB,GAC3B,OAAO,CAAC,oBAAoB,CAAC,CAAC;IAEjC,wFAAwF;IACxF,0BAA0B,CACtB,OAAO,EAAE,mBAAmB,GAC7B,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAExC,0EAA0E;IAC1E,wBAAwB,CACpB,OAAO,EAAE,iBAAiB,GAC3B,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAExC;;;OAGG;IACH,8BAA8B,CAC1B,OAAO,EAAE,uBAAuB,GACjC,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAC;IAExC,+CAA+C;IAC/C,aAAa,IAAI,UAAU,CAAC;IAE5B,kCAAkC;IAClC,SAAS,IAAI,MAAM,CAAC;IAEpB,gGAAgG;IAChG,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEhC,sBAAsB;IACtB,UAAU,IAAI,IAAI,CAAC;IAEnB,+BAA+B;IAC/B,cAAc,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IAEzC,gEAAgE;IAChE,OAAO,CAAC,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CACnD"}