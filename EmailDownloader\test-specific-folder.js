// Test script to access the specific Outlook folder from the URL
require('dotenv').config();
const msal = require('@azure/msal-node');
const axios = require('axios');

async function testSpecificFolder() {
    console.log("Testing access to specific Outlook folder...\n");
    
    // Create MSAL configuration
    const msalConfig = {
        auth: {
            clientId: process.env.CLIENT_ID,
            authority: `https://login.microsoftonline.com/${process.env.TENANT_ID}`,
            clientSecret: process.env.CLIENT_SECRET
        }
    };
    
    try {
        // Get access token
        console.log("1. Getting access token...");
        const cca = new msal.ConfidentialClientApplication(msalConfig);
        const tokenRequest = { scopes: ['https://graph.microsoft.com/.default'] };
        const response = await cca.acquireTokenByClientCredential(tokenRequest);
        console.log("✅ Access token acquired successfully\n");
        
        // Create API client
        const api = axios.create({
            baseURL: 'https://graph.microsoft.com/v1.0',
            headers: { Authorization: `Bearer ${response.accessToken}` }
        });
        
        // Extract folder ID from the Outlook URL
        const outlookUrl = "https://outlook.office365.com/mail/AQMkADUwZmE2ZDI0LTE1ZmEtNDUyYi04M2EwLTE5MjEyOGQzZDFkZgAuAAAD930exyQyR0aObP4N%2BrNeUQEAa7q%2FZGwztk2Az3te%2FyoUBAAAAuI4AAAA";
        const encodedFolderId = outlookUrl.split('/mail/')[1];
        
        console.log("2. Extracted folder information:");
        console.log(`   Encoded Folder ID: ${encodedFolderId}\n`);
        
        // Try different approaches to access the folder
        console.log("3. Testing different API endpoints...\n");
        
        // Test 1: Try to get all mail folders first
        console.log("Test 1: Getting all mail folders...");
        try {
            const foldersResponse = await api.get(`/me/mailFolders`);
            console.log(`✅ Found ${foldersResponse.data.value.length} mail folders:`);
            foldersResponse.data.value.slice(0, 10).forEach((folder, index) => {
                console.log(`   ${index + 1}. ${folder.displayName} (ID: ${folder.id.substring(0, 20)}...)`);
            });
            
            // Look for 'bahrain' folder
            const bahrainFolder = foldersResponse.data.value.find(f => 
                f.displayName.toLowerCase().includes('bahrain')
            );
            if (bahrainFolder) {
                console.log(`\n✅ Found 'bahrain' folder: ${bahrainFolder.displayName}`);
                console.log(`   Folder ID: ${bahrainFolder.id}`);
                
                // Try to get messages from this folder
                console.log("\nTest 2: Getting messages from bahrain folder...");
                try {
                    const messagesResponse = await api.get(`/me/mailFolders/${bahrainFolder.id}/messages?$top=5`);
                    console.log(`✅ Found ${messagesResponse.data.value.length} messages in bahrain folder`);
                    messagesResponse.data.value.forEach((msg, index) => {
                        console.log(`   ${index + 1}. ${msg.subject || 'No Subject'} (${new Date(msg.receivedDateTime).toLocaleDateString()})`);
                    });
                } catch (error) {
                    console.log(`❌ Failed to get messages: ${error.response?.data?.error?.message || error.message}`);
                }
            } else {
                console.log("\n❌ 'bahrain' folder not found in the list");
            }
            
        } catch (error) {
            console.log(`❌ Failed to get folders: ${error.response?.data?.error?.message || error.message}`);
            
            // Try with application permissions endpoint
            console.log("\nTest 2: Trying with application permissions...");
            try {
                const foldersResponse = await api.get(`/users/${process.env.MAIL_USER_ID}/mailFolders`);
                console.log(`✅ Found ${foldersResponse.data.value.length} mail folders (app permissions)`);
            } catch (appError) {
                console.log(`❌ Application permissions also failed: ${appError.response?.data?.error?.message || appError.message}`);
            }
        }
        
        // Test 3: Try to decode and use the folder ID from URL
        console.log("\nTest 3: Trying to decode folder ID from URL...");
        try {
            // The folder ID might be base64 encoded or in a specific format
            // Let's try to use it directly
            const possibleFolderId = decodeURIComponent(encodedFolderId);
            console.log(`   Decoded ID: ${possibleFolderId.substring(0, 50)}...`);
            
            // Try to access this folder directly
            try {
                const folderResponse = await api.get(`/me/mailFolders/${possibleFolderId}`);
                console.log(`✅ Successfully accessed folder: ${folderResponse.data.displayName}`);
            } catch (error) {
                console.log(`❌ Could not access folder directly: ${error.response?.data?.error?.message || error.message}`);
            }
        } catch (error) {
            console.log(`❌ Could not decode folder ID: ${error.message}`);
        }
        
        console.log("\n" + "=".repeat(60));
        console.log("SUMMARY:");
        console.log("If any tests above succeeded, your permissions are working!");
        console.log("If all tests failed, you still need admin consent for Microsoft Graph permissions.");
        console.log("=".repeat(60));
        
    } catch (error) {
        console.error("❌ Authentication failed:", error.message);
        console.error("Check your CLIENT_ID, CLIENT_SECRET, and TENANT_ID in the .env file");
    }
}

// Run the test
testSpecificFolder().catch(console.error);
