{"version": 3, "file": "Authority.d.ts", "sourceRoot": "", "sources": ["../../src/authority/Authority.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AACnD,OAAO,EAEH,oBAAoB,EACvB,MAAM,2BAA2B,CAAC;AAEnC,OAAO,EAAE,IAAI,EAAE,MAAM,gBAAgB,CAAC;AAKtC,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAiB9D,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,qCAAqC,CAAC;AAEpE,OAAO,EACH,gBAAgB,EAEhB,sBAAsB,EACzB,MAAM,uBAAuB,CAAC;AAS/B,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AAErE,OAAO,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AAEvE,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AAC1E,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,gDAAgD,CAAC;AAKpF;;;;GAIG;AACH,qBAAa,SAAS;IAElB,OAAO,CAAC,mBAAmB,CAAY;IAEvC,OAAO,CAAC,gCAAgC,CAAc;IAEtD,SAAS,CAAC,gBAAgB,EAAE,cAAc,CAAC;IAE3C,SAAS,CAAC,YAAY,EAAE,aAAa,CAAC;IAEtC,OAAO,CAAC,gBAAgB,CAAmB;IAE3C,OAAO,CAAC,QAAQ,CAA0B;IAE1C,OAAO,CAAC,eAAe,CAAkB;IAElC,uBAAuB,EAAE,uBAAuB,CAAC;IAExD,OAAO,CAAC,MAAM,CAAS;IAEvB,SAAS,CAAC,iBAAiB,EAAE,kBAAkB,GAAG,SAAS,CAAC;IAE5D,SAAS,CAAC,aAAa,EAAE,MAAM,CAAC;IAEhC,OAAO,CAAC,eAAe,CAAU;IAEjC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAMjC;gBAGC,SAAS,EAAE,MAAM,EACjB,gBAAgB,EAAE,cAAc,EAChC,YAAY,EAAE,aAAa,EAC3B,gBAAgB,EAAE,gBAAgB,EAClC,MAAM,EAAE,MAAM,EACd,aAAa,EAAE,MAAM,EACrB,iBAAiB,CAAC,EAAE,kBAAkB,EACtC,eAAe,CAAC,EAAE,OAAO;IAwB7B;;;;OAIG;IACH,OAAO,CAAC,gBAAgB;IAqBxB,IAAW,aAAa,IAAI,aAAa,CAExC;IAED;;OAEG;IACH,IAAW,YAAY,IAAI,YAAY,CAEtC;IAED;;OAEG;IACH,IAAW,OAAO,IAAI,gBAAgB,CAErC;IAED;;OAEG;IACH,IAAW,kBAAkB,IAAI,MAAM,CAEtC;IAED;;OAEG;IACH,IAAW,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAIxC;IAED;;OAEG;IACH,IAAW,+BAA+B,IAAI,IAAI,CAOjD;IAED;;OAEG;IACH,IAAW,eAAe,IAAI,MAAM,CAEnC;IAED;;OAEG;IACH,IAAW,MAAM,IAAI,MAAM,CAE1B;IAED;;OAEG;IACH,IAAW,qBAAqB,IAAI,MAAM,CAQzC;IAED;;OAEG;IACH,IAAW,aAAa,IAAI,MAAM,CAQjC;IAED,IAAW,kBAAkB,IAAI,MAAM,CAUtC;IAED;;OAEG;IACH,IAAW,kBAAkB,IAAI,MAAM,CActC;IAED;;OAEG;IACH,IAAW,qBAAqB,IAAI,MAAM,CAQzC;IAED;;OAEG;IACH,IAAW,OAAO,IAAI,MAAM,CAQ3B;IAED;;;;OAIG;IACH,OAAO,CAAC,gBAAgB;IAWxB;;;OAGG;IACH,OAAO,CAAC,aAAa;IAIrB;;;OAGG;IACH,OAAO,CAAC,WAAW;IA2CnB;;OAEG;IACH,SAAS,KAAK,kCAAkC,IAAI,MAAM,CAWzD;IAED;;OAEG;IACH,iBAAiB,IAAI,OAAO;IAI5B;;;OAGG;IACU,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC;IAsCnD;;;;OAIG;IACH,OAAO,CAAC,wBAAwB;IAuBhC;;;;;;OAMG;IACH,OAAO,CAAC,oBAAoB;IAyB5B;;;OAGG;YACW,sBAAsB;IA+EpC;;;;;OAKG;IACH,OAAO,CAAC,sCAAsC;IAuE9C;;;;;OAKG;IACH,OAAO,CAAC,mBAAmB;IAc3B;;OAEG;IACH,OAAO,CAAC,6BAA6B;IAgBrC;;;;OAIG;YACW,8BAA8B;IA0C5C;;OAEG;IACH,OAAO,CAAC,sCAAsC;IAQ9C;;;OAGG;YACW,qCAAqC;IAwDnD;;;;;OAKG;YACW,4BAA4B;IAqC1C,OAAO,CAAC,4CAA4C;IAoFpD;;OAEG;IACH,OAAO,CAAC,mCAAmC;IA6D3C;;;;OAIG;YACW,oCAAoC;IAqGlD;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAa5B;;;;OAIG;IACH,MAAM,CAAC,iBAAiB,CACpB,eAAe,EAAE,MAAM,EACvB,iBAAiB,CAAC,EAAE,iBAAiB,GACtC,MAAM;IAkBT;;;OAGG;IACH,MAAM,CAAC,oCAAoC,CACvC,IAAI,EAAE,MAAM,GACb,sBAAsB;IAQzB;;OAEG;IACH,iBAAiB,IAAI,MAAM;IAY3B;;;OAGG;IACH,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAI9B;;;OAGG;IACH,gCAAgC,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAIvD;;;;;OAKG;IACH,MAAM,CAAC,sBAAsB,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAIpD;;;;;OAKG;IACH,MAAM,CAAC,4BAA4B,CAC/B,IAAI,EAAE,MAAM,EACZ,MAAM,EAAE,MAAM,EACd,WAAW,CAAC,EAAE,MAAM,GACrB,MAAM;IAyBT;;;;;OAKG;IACH,MAAM,CAAC,8BAA8B,CACjC,QAAQ,EAAE,oBAAoB,EAC9B,WAAW,EAAE,MAAM,GACpB,oBAAoB;IAyBvB;;;;;;;;OAQG;IACH,MAAM,CAAC,sBAAsB,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM;CAmB3D;AAED;;GAEG;AACH,wBAAgB,4BAA4B,CACxC,SAAS,EAAE,MAAM,GAClB,MAAM,GAAG,SAAS,CAsBpB;AAED,wBAAgB,kBAAkB,CAAC,YAAY,EAAE,MAAM,GAAG,MAAM,CAI/D;AAED,wBAAgB,2BAA2B,CACvC,WAAW,EAAE,OAAO,CAAC,gBAAgB,CAAC,GACvC,sBAAsB,CAoBxB"}