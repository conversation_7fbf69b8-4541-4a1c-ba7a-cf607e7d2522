{"version": 3, "file": "AuthorityType.js", "sources": ["../../src/authority/AuthorityType.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\n/**\r\n * Authority types supported by MSAL.\r\n */\r\nexport enum AuthorityType {\r\n    Default,\r\n    Adfs,\r\n    Dsts,\r\n    Ciam\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAEH;;AAEG;IACS,cAKX;AALD,CAAA,UAAY,aAAa,EAAA;AACrB,IAAA,aAAA,CAAA,aAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACR,CAAC,EALW,aAAa,KAAb,aAAa,GAKxB,EAAA,CAAA,CAAA;;;;"}