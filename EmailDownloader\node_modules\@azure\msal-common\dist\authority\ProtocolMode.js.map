{"version": 3, "file": "ProtocolMode.js", "sources": ["../../src/authority/ProtocolMode.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\n/**\r\n * Protocol modes supported by MSAL.\r\n */\r\nexport enum ProtocolMode {\r\n    AAD = \"AAD\",\r\n    OIDC = \"OIDC\"\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAEH;;AAEG;IACS,aAGX;AAHD,CAAA,UAAY,YAAY,EAAA;AACpB,IAAA,YAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX,IAAA,YAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACjB,CAAC,EAHW,YAAY,KAAZ,YAAY,GAGvB,EAAA,CAAA,CAAA;;;;"}