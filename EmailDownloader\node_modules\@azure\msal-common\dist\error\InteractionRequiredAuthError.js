/*! @azure/msal-common v13.3.1 2023-10-27 */
'use strict';
import { __extends } from '../_virtual/_tslib.js';
import { Constants } from '../utils/Constants.js';
import { AuthError } from './AuthError.js';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * InteractionRequiredServerErrorMessage contains string constants used by error codes and messages returned by the server indicating interaction is required
 */
var InteractionRequiredServerErrorMessage = [
    "interaction_required",
    "consent_required",
    "login_required"
];
var InteractionRequiredAuthSubErrorMessage = [
    "message_only",
    "additional_action",
    "basic_action",
    "user_password_expired",
    "consent_required"
];
/**
 * Interaction required errors defined by the SDK
 */
var InteractionRequiredAuthErrorMessage = {
    noTokensFoundError: {
        code: "no_tokens_found",
        desc: "No refresh token found in the cache. Please sign-in."
    },
    native_account_unavailable: {
        code: "native_account_unavailable",
        desc: "The requested account is not available in the native broker. It may have been deleted or logged out. Please sign-in again using an interactive API."
    }
};
/**
 * Error thrown when user interaction is required.
 */
var InteractionRequiredAuthError = /** @class */ (function (_super) {
    __extends(InteractionRequiredAuthError, _super);
    function InteractionRequiredAuthError(errorCode, errorMessage, subError, timestamp, traceId, correlationId, claims) {
        var _this = _super.call(this, errorCode, errorMessage, subError) || this;
        Object.setPrototypeOf(_this, InteractionRequiredAuthError.prototype);
        _this.timestamp = timestamp || Constants.EMPTY_STRING;
        _this.traceId = traceId || Constants.EMPTY_STRING;
        _this.correlationId = correlationId || Constants.EMPTY_STRING;
        _this.claims = claims || Constants.EMPTY_STRING;
        _this.name = "InteractionRequiredAuthError";
        return _this;
    }
    /**
     * Helper function used to determine if an error thrown by the server requires interaction to resolve
     * @param errorCode
     * @param errorString
     * @param subError
     */
    InteractionRequiredAuthError.isInteractionRequiredError = function (errorCode, errorString, subError) {
        var isInteractionRequiredErrorCode = !!errorCode && InteractionRequiredServerErrorMessage.indexOf(errorCode) > -1;
        var isInteractionRequiredSubError = !!subError && InteractionRequiredAuthSubErrorMessage.indexOf(subError) > -1;
        var isInteractionRequiredErrorDesc = !!errorString && InteractionRequiredServerErrorMessage.some(function (irErrorCode) {
            return errorString.indexOf(irErrorCode) > -1;
        });
        return isInteractionRequiredErrorCode || isInteractionRequiredErrorDesc || isInteractionRequiredSubError;
    };
    /**
     * Creates an error thrown when the authorization code required for a token request is null or empty.
     */
    InteractionRequiredAuthError.createNoTokensFoundError = function () {
        return new InteractionRequiredAuthError(InteractionRequiredAuthErrorMessage.noTokensFoundError.code, InteractionRequiredAuthErrorMessage.noTokensFoundError.desc);
    };
    /**
     * Creates an error thrown when the native broker returns ACCOUNT_UNAVAILABLE status, indicating that the account was removed and interactive sign-in is required
     * @returns
     */
    InteractionRequiredAuthError.createNativeAccountUnavailableError = function () {
        return new InteractionRequiredAuthError(InteractionRequiredAuthErrorMessage.native_account_unavailable.code, InteractionRequiredAuthErrorMessage.native_account_unavailable.desc);
    };
    return InteractionRequiredAuthError;
}(AuthError));

export { InteractionRequiredAuthError, InteractionRequiredAuthErrorMessage, InteractionRequiredAuthSubErrorMessage, InteractionRequiredServerErrorMessage };
//# sourceMappingURL=InteractionRequiredAuthError.js.map
