{"version": 3, "file": "CloudInstanceDiscoveryErrorResponse.js", "sources": ["../../src/authority/CloudInstanceDiscoveryErrorResponse.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\n/**\r\n * The OpenID Configuration Endpoint Response type. Used by the authority class to get relevant OAuth endpoints.\r\n */\r\nexport type CloudInstanceDiscoveryErrorResponse = {\r\n    error: String;\r\n    error_description: String;\r\n    error_codes?: Array<Number>;\r\n    timestamp?: String;\r\n    trace_id?: String;\r\n    correlation_id?: String;\r\n    error_uri?: String;\r\n};\r\n\r\nexport function isCloudInstanceDiscoveryErrorResponse(response: object): boolean {\r\n    return (\r\n        response.hasOwnProperty(\"error\") &&\r\n        response.hasOwnProperty(\"error_description\")\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;AAAA;;;AAGG;AAeG,SAAU,qCAAqC,CAAC,QAAgB,EAAA;AAClE,IAAA,QACI,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC;AAChC,QAAA,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,EAC9C;AACN;;;;"}