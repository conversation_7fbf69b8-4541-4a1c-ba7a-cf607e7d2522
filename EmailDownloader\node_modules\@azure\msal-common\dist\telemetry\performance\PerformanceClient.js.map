{"version": 3, "file": "PerformanceClient.js", "sources": ["../../../src/telemetry/performance/PerformanceClient.ts"], "sourcesContent": ["/*\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n * Licensed under the MIT License.\r\n */\r\n\r\nimport { ApplicationTelemetry } from \"../../config/ClientConfiguration\";\r\nimport { Logger } from \"../../logger/Logger\";\r\nimport {\r\n    InProgressPerformanceEvent,\r\n    IPerformanceClient,\r\n    PerformanceCallbackFunction,\r\n    QueueMeasurement\r\n} from \"./IPerformanceClient\";\r\nimport { IPerformanceMeasurement } from \"./IPerformanceMeasurement\";\r\nimport {\r\n    Counters,\r\n    IntFields,\r\n    PerformanceEvent,\r\n    PerformanceEvents,\r\n    PerformanceEventStatus,\r\n    StaticFields\r\n} from \"./PerformanceEvent\";\r\n\r\nexport interface PreQueueEvent {\r\n    name: PerformanceEvents;\r\n    time: number;\r\n}\r\n\r\nexport abstract class PerformanceClient implements IPerformanceClient {\r\n    protected authority: string;\r\n    protected libraryName: string;\r\n    protected libraryVersion: string;\r\n    protected applicationTelemetry: ApplicationTelemetry;\r\n    protected clientId: string;\r\n    protected logger: Logger;\r\n    protected callbacks: Map<string, PerformanceCallbackFunction>;\r\n\r\n    /**\r\n     * Multiple events with the same correlation id.\r\n     * @protected\r\n     * @type {Map<string, PerformanceEvent>}\r\n     */\r\n    protected eventsByCorrelationId: Map<string, PerformanceEvent>;\r\n\r\n    /**\r\n     * Map of pre-queue times by correlation Id\r\n     *\r\n     * @protected\r\n     * @type {Map<string, PreQueueEvent>}\r\n     */\r\n    protected preQueueTimeByCorrelationId: Map<string, PreQueueEvent>;\r\n\r\n    /**\r\n     * Map of queue measurements by correlation Id\r\n     *\r\n     * @protected\r\n     * @type {Map<string, Array<QueueMeasurement>>}\r\n     */\r\n    protected queueMeasurements: Map<string, Array<QueueMeasurement>>;\r\n\r\n    /**\r\n     * Creates an instance of PerformanceClient,\r\n     * an abstract class containing core performance telemetry logic.\r\n     *\r\n     * @constructor\r\n     * @param {string} clientId Client ID of the application\r\n     * @param {string} authority Authority used by the application\r\n     * @param {Logger} logger Logger used by the application\r\n     * @param {string} libraryName Name of the library\r\n     * @param {string} libraryVersion Version of the library\r\n     */\r\n    constructor(clientId: string, authority: string, logger: Logger, libraryName: string, libraryVersion: string, applicationTelemetry: ApplicationTelemetry) {\r\n        this.authority = authority;\r\n        this.libraryName = libraryName;\r\n        this.libraryVersion = libraryVersion;\r\n        this.applicationTelemetry = applicationTelemetry;\r\n        this.clientId = clientId;\r\n        this.logger = logger;\r\n        this.callbacks = new Map();\r\n        this.eventsByCorrelationId = new Map();\r\n        this.queueMeasurements = new Map();\r\n        this.preQueueTimeByCorrelationId = new Map();\r\n    }\r\n\r\n    /**\r\n     * Generates and returns a unique id, typically a guid.\r\n     *\r\n     * @abstract\r\n     * @returns {string}\r\n     */\r\n    abstract generateId(): string;\r\n\r\n    /**\r\n     * Starts and returns an platform-specific implementation of IPerformanceMeasurement.\r\n     * Note: this function can be changed to abstract at the next major version bump.\r\n     *\r\n     * @param {string} measureName\r\n     * @param {string} correlationId\r\n     * @returns {IPerformanceMeasurement}\r\n     */\r\n    /* eslint-disable-next-line @typescript-eslint/no-unused-vars */\r\n    startPerformanceMeasurement(measureName: string, correlationId: string): IPerformanceMeasurement {\r\n        return {} as IPerformanceMeasurement;\r\n    }\r\n\r\n    /**\r\n     * Starts and returns an platform-specific implementation of IPerformanceMeasurement.\r\n     * Note: this incorrectly-named function will be removed at the next major version bump.\r\n     *\r\n     * @param {string} measureName\r\n     * @param {string} correlationId\r\n     * @returns {IPerformanceMeasurement}\r\n     */\r\n    /* eslint-disable-next-line @typescript-eslint/no-unused-vars */\r\n    startPerformanceMeasuremeant(measureName: string, correlationId: string): IPerformanceMeasurement {\r\n        return {} as IPerformanceMeasurement;\r\n    }\r\n\r\n    /**\r\n     * Sets pre-queue time by correlation Id\r\n     *\r\n     * @abstract\r\n     * @param {PerformanceEvents} eventName\r\n     * @param {string} correlationId\r\n     * @returns\r\n     */\r\n    abstract setPreQueueTime(eventName: PerformanceEvents, correlationId?: string): void;\r\n\r\n    /**\r\n     * Get integral fields.\r\n     * Override to change the set.\r\n     */\r\n    getIntFields(): ReadonlySet<string> {\r\n        return IntFields;\r\n    }\r\n\r\n    /**\r\n     * Gets map of pre-queue times by correlation Id\r\n     *\r\n     * @param {PerformanceEvents} eventName\r\n     * @param {string} correlationId\r\n     * @returns {number}\r\n     */\r\n    getPreQueueTime(eventName: PerformanceEvents, correlationId: string): number | void {\r\n        const preQueueEvent: PreQueueEvent | undefined = this.preQueueTimeByCorrelationId.get(correlationId);\r\n\r\n        if (!preQueueEvent) {\r\n            this.logger.trace(`PerformanceClient.getPreQueueTime: no pre-queue times found for correlationId: ${correlationId}, unable to add queue measurement`);\r\n            return;\r\n        } else if (preQueueEvent.name !== eventName) {\r\n            this.logger.trace(`PerformanceClient.getPreQueueTime: no pre-queue time found for ${eventName}, unable to add queue measurement`);\r\n            return;\r\n        }\r\n\r\n        return preQueueEvent.time;\r\n    }\r\n\r\n    /**\r\n     * Calculates the difference between current time and time when function was queued.\r\n     * Note: It is possible to have 0 as the queue time if the current time and the queued time was the same.\r\n     *\r\n     * @param {number} preQueueTime\r\n     * @param {number} currentTime\r\n     * @returns {number}\r\n     */\r\n    calculateQueuedTime(preQueueTime: number, currentTime: number): number {\r\n        if (preQueueTime < 1) {\r\n            this.logger.trace(`PerformanceClient: preQueueTime should be a positive integer and not ${preQueueTime}`);\r\n            return 0;\r\n        }\r\n\r\n        if (currentTime < 1) {\r\n            this.logger.trace(`PerformanceClient: currentTime should be a positive integer and not ${currentTime}`);\r\n            return 0;\r\n        }\r\n\r\n        if (currentTime < preQueueTime) {\r\n            this.logger.trace(\"PerformanceClient: currentTime is less than preQueueTime, check how time is being retrieved\");\r\n            return 0;\r\n        }\r\n\r\n        return currentTime-preQueueTime;\r\n    }\r\n\r\n    /**\r\n     * Adds queue measurement time to QueueMeasurements array for given correlation ID.\r\n     *\r\n     * @param {PerformanceEvents} eventName\r\n     * @param {?string} correlationId\r\n     * @param {?number} queueTime\r\n     * @param {?boolean} manuallyCompleted - indicator for manually completed queue measurements\r\n     * @returns\r\n     */\r\n    addQueueMeasurement(eventName: PerformanceEvents, correlationId?: string, queueTime?: number, manuallyCompleted?: boolean): void {\r\n        if (!correlationId) {\r\n            this.logger.trace(`PerformanceClient.addQueueMeasurement: correlationId not provided for ${eventName}, cannot add queue measurement`);\r\n            return;\r\n        }\r\n\r\n        if (queueTime === 0) {\r\n            // Possible for there to be no queue time after calculation\r\n            this.logger.trace(`PerformanceClient.addQueueMeasurement: queue time provided for ${eventName} is ${queueTime}`);\r\n        } else if (!queueTime) {\r\n            this.logger.trace(`PerformanceClient.addQueueMeasurement: no queue time provided for ${eventName}`);\r\n            return;\r\n        }\r\n\r\n        const queueMeasurement: QueueMeasurement = {eventName, queueTime, manuallyCompleted};\r\n\r\n        // Adds to existing correlation Id if present in queueMeasurements\r\n        const existingMeasurements = this.queueMeasurements.get(correlationId);\r\n        if (existingMeasurements) {\r\n            existingMeasurements.push(queueMeasurement);\r\n            this.queueMeasurements.set(correlationId, existingMeasurements);\r\n        } else {\r\n            // Sets new correlation Id if not present in queueMeasurements\r\n            this.logger.trace(`PerformanceClient.addQueueMeasurement: adding correlationId ${correlationId} to queue measurements`);\r\n            const measurementArray = [queueMeasurement];\r\n            this.queueMeasurements.set(correlationId, measurementArray);\r\n        }\r\n        // Delete processed pre-queue event.\r\n        this.preQueueTimeByCorrelationId.delete(correlationId);\r\n    }\r\n\r\n    /**\r\n     * Starts measuring performance for a given operation. Returns a function that should be used to end the measurement.\r\n     *\r\n     * @param {PerformanceEvents} measureName\r\n     * @param {?string} [correlationId]\r\n     * @returns {InProgressPerformanceEvent}\r\n     */\r\n    startMeasurement(measureName: PerformanceEvents, correlationId?: string): InProgressPerformanceEvent {\r\n        // Generate a placeholder correlation if the request does not provide one\r\n        const eventCorrelationId = correlationId || this.generateId();\r\n        if (!correlationId) {\r\n            this.logger.info(`PerformanceClient: No correlation id provided for ${measureName}, generating`, eventCorrelationId);\r\n        }\r\n\r\n        // Duplicate code to address spelling error will be removed at the next major version bump.\r\n        this.logger.trace(`PerformanceClient: Performance measurement started for ${measureName}`, eventCorrelationId);\r\n        const performanceMeasurement = this.startPerformanceMeasuremeant(measureName, eventCorrelationId);\r\n        performanceMeasurement.startMeasurement();\r\n\r\n        const inProgressEvent: PerformanceEvent = {\r\n            eventId: this.generateId(),\r\n            status: PerformanceEventStatus.InProgress,\r\n            authority: this.authority,\r\n            libraryName: this.libraryName,\r\n            libraryVersion: this.libraryVersion,\r\n            clientId: this.clientId,\r\n            name: measureName,\r\n            startTimeMs: Date.now(),\r\n            correlationId: eventCorrelationId,\r\n            appName: this.applicationTelemetry?.appName,\r\n            appVersion: this.applicationTelemetry?.appVersion,\r\n        };\r\n\r\n        // Store in progress events so they can be discarded if not ended properly\r\n        this.cacheEventByCorrelationId(inProgressEvent);\r\n\r\n        // Return the event and functions the caller can use to properly end/flush the measurement\r\n        return {\r\n            endMeasurement: (event?: Partial<PerformanceEvent>): PerformanceEvent | null => {\r\n                return this.endMeasurement({\r\n                    // Initial set of event properties\r\n                    ...inProgressEvent,\r\n                    // Properties set when event ends\r\n                    ...event\r\n                },\r\n                performanceMeasurement);\r\n            },\r\n            discardMeasurement: () => {\r\n                return this.discardMeasurements(inProgressEvent.correlationId);\r\n            },\r\n            addStaticFields: (fields: StaticFields) => {\r\n                return this.addStaticFields(fields, inProgressEvent.correlationId);\r\n            },\r\n            increment: (counters: Counters) => {\r\n                return this.increment(counters, inProgressEvent.correlationId);\r\n            },\r\n            measurement: performanceMeasurement,\r\n            event: inProgressEvent\r\n        };\r\n\r\n    }\r\n\r\n    /**\r\n     * Stops measuring the performance for an operation. Should only be called directly by PerformanceClient classes,\r\n     * as consumers should instead use the function returned by startMeasurement.\r\n     * Adds a new field named as \"[event name]DurationMs\" for sub-measurements, completes and emits an event\r\n     * otherwise.\r\n     *\r\n     * @param {PerformanceEvent} event\r\n     * @param {IPerformanceMeasurement} measurement\r\n     * @returns {(PerformanceEvent | null)}\r\n     */\r\n    endMeasurement(event: PerformanceEvent, measurement?: IPerformanceMeasurement): PerformanceEvent | null {\r\n        const rootEvent: PerformanceEvent | undefined = this.eventsByCorrelationId.get(event.correlationId);\r\n        if (!rootEvent) {\r\n            this.logger.trace(`PerformanceClient: Measurement not found for ${event.eventId}`, event.correlationId);\r\n            return null;\r\n        }\r\n\r\n        const isRoot = event.eventId === rootEvent.eventId;\r\n        let queueInfo = {\r\n            totalQueueTime: 0,\r\n            totalQueueCount: 0,\r\n            manuallyCompletedCount: 0\r\n        };\r\n        if (isRoot) {\r\n            queueInfo = this.getQueueInfo(event.correlationId);\r\n            this.discardCache(rootEvent.correlationId);\r\n        } else {\r\n            rootEvent.incompleteSubMeasurements?.delete(event.eventId);\r\n        }\r\n\r\n        measurement?.endMeasurement();\r\n        const durationMs = measurement?.flushMeasurement();\r\n        // null indicates no measurement was taken (e.g. needed performance APIs not present)\r\n        if (!durationMs) {\r\n            this.logger.trace(\"PerformanceClient: Performance measurement not taken\", rootEvent.correlationId);\r\n            return null;\r\n        }\r\n\r\n        this.logger.trace(`PerformanceClient: Performance measurement ended for ${event.name}: ${durationMs} ms`, event.correlationId);\r\n\r\n        // Add sub-measurement attribute to root event.\r\n        if (!isRoot) {\r\n            rootEvent[event.name + \"DurationMs\"] = Math.floor(durationMs);\r\n            return { ...rootEvent };\r\n        }\r\n\r\n        let finalEvent: PerformanceEvent = { ...rootEvent, ...event };\r\n        let incompleteSubsCount: number = 0;\r\n        // Incomplete sub-measurements are discarded. They are likely an instrumentation bug that should be fixed.\r\n        finalEvent.incompleteSubMeasurements?.forEach(subMeasurement => {\r\n            this.logger.trace(`PerformanceClient: Incomplete submeasurement ${subMeasurement.name} found for ${event.name}`, finalEvent.correlationId);\r\n            incompleteSubsCount++;\r\n        });\r\n        finalEvent.incompleteSubMeasurements = undefined;\r\n\r\n        finalEvent = {\r\n            ...finalEvent,\r\n            durationMs: Math.round(durationMs),\r\n            queuedTimeMs: queueInfo.totalQueueTime,\r\n            queuedCount: queueInfo.totalQueueCount,\r\n            queuedManuallyCompletedCount: queueInfo.manuallyCompletedCount,\r\n            status: PerformanceEventStatus.Completed,\r\n            incompleteSubsCount\r\n        };\r\n        this.truncateIntegralFields(finalEvent, this.getIntFields());\r\n        this.emitEvents([finalEvent], event.correlationId);\r\n\r\n        return finalEvent;\r\n    }\r\n\r\n    /**\r\n     * Saves extra information to be emitted when the measurements are flushed\r\n     * @param fields\r\n     * @param correlationId\r\n     */\r\n    addStaticFields(fields: StaticFields, correlationId: string) : void {\r\n        this.logger.trace(\"PerformanceClient: Updating static fields\");\r\n        const event = this.eventsByCorrelationId.get(correlationId);\r\n        if (event) {\r\n            this.eventsByCorrelationId.set(correlationId, {...event, ...fields});\r\n        } else {\r\n            this.logger.trace(\"PerformanceClient: Event not found for\", correlationId);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Increment counters to be emitted when the measurements are flushed\r\n     * @param counters {Counters}\r\n     * @param correlationId {string} correlation identifier\r\n     */\r\n    increment(counters: Counters, correlationId: string): void {\r\n        this.logger.trace(\"PerformanceClient: Updating counters\");\r\n        const event = this.eventsByCorrelationId.get(correlationId);\r\n        if (event) {\r\n            for (const counter in counters) {\r\n                if (!event.hasOwnProperty(counter)) {\r\n                    event[counter] = 0;\r\n                }\r\n                event[counter] += counters[counter];\r\n            }\r\n        } else {\r\n            this.logger.trace(\"PerformanceClient: Event not found for\", correlationId);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Upserts event into event cache.\r\n     * First key is the correlation id, second key is the event id.\r\n     * Allows for events to be grouped by correlation id,\r\n     * and to easily allow for properties on them to be updated.\r\n     *\r\n     * @private\r\n     * @param {PerformanceEvent} event\r\n     */\r\n    private cacheEventByCorrelationId(event: PerformanceEvent) {\r\n        const rootEvent = this.eventsByCorrelationId.get(event.correlationId);\r\n        if (rootEvent) {\r\n            this.logger.trace(`PerformanceClient: Performance measurement for ${event.name} added/updated`, event.correlationId);\r\n            rootEvent.incompleteSubMeasurements = rootEvent.incompleteSubMeasurements || new Map();\r\n            rootEvent.incompleteSubMeasurements.set(event.eventId, {name: event.name, startTimeMs: event.startTimeMs });\r\n        } else {\r\n            this.logger.trace(`PerformanceClient: Performance measurement for ${event.name} started`, event.correlationId);\r\n            this.eventsByCorrelationId.set(event.correlationId, { ...event });\r\n        }\r\n    }\r\n\r\n    private getQueueInfo(correlationId: string): { totalQueueTime: number, totalQueueCount: number, manuallyCompletedCount: number } {\r\n        const queueMeasurementForCorrelationId = this.queueMeasurements.get(correlationId);\r\n        if (!queueMeasurementForCorrelationId) {\r\n            this.logger.trace(`PerformanceClient: no queue measurements found for for correlationId: ${correlationId}`);\r\n        }\r\n\r\n        let totalQueueTime = 0;\r\n        let totalQueueCount = 0;\r\n        let manuallyCompletedCount = 0;\r\n        queueMeasurementForCorrelationId?.forEach((measurement) => {\r\n            totalQueueTime += measurement.queueTime;\r\n            totalQueueCount++;\r\n            manuallyCompletedCount += measurement.manuallyCompleted ? 1 : 0;\r\n        });\r\n\r\n        return {\r\n            totalQueueTime,\r\n            totalQueueCount,\r\n            manuallyCompletedCount\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Removes measurements for a given correlation id.\r\n     *\r\n     * @param {string} correlationId\r\n     */\r\n    discardMeasurements(correlationId: string): void {\r\n        this.logger.trace(\"PerformanceClient: Performance measurements discarded\", correlationId);\r\n        this.eventsByCorrelationId.delete(correlationId);\r\n    }\r\n\r\n    /**\r\n     * Removes cache for a given correlation id.\r\n     *\r\n     * @param {string} correlationId correlation identifier\r\n     */\r\n    private discardCache(correlationId: string): void {\r\n        this.discardMeasurements(correlationId);\r\n\r\n        this.logger.trace(\"PerformanceClient: QueueMeasurements discarded\", correlationId);\r\n        this.queueMeasurements.delete(correlationId);\r\n\r\n        this.logger.trace(\"PerformanceClient: Pre-queue times discarded\", correlationId);\r\n        this.preQueueTimeByCorrelationId.delete(correlationId);\r\n    }\r\n\r\n    /**\r\n     * Registers a callback function to receive performance events.\r\n     *\r\n     * @param {PerformanceCallbackFunction} callback\r\n     * @returns {string}\r\n     */\r\n    addPerformanceCallback(callback: PerformanceCallbackFunction): string {\r\n        const callbackId = this.generateId();\r\n        this.callbacks.set(callbackId, callback);\r\n        this.logger.verbose(`PerformanceClient: Performance callback registered with id: ${callbackId}`);\r\n\r\n        return callbackId;\r\n    }\r\n\r\n    /**\r\n     * Removes a callback registered with addPerformanceCallback.\r\n     *\r\n     * @param {string} callbackId\r\n     * @returns {boolean}\r\n     */\r\n    removePerformanceCallback(callbackId: string): boolean {\r\n        const result = this.callbacks.delete(callbackId);\r\n\r\n        if (result) {\r\n            this.logger.verbose(`PerformanceClient: Performance callback ${callbackId} removed.`);\r\n        } else {\r\n            this.logger.verbose(`PerformanceClient: Performance callback ${callbackId} not removed.`);\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    /**\r\n     * Emits events to all registered callbacks.\r\n     *\r\n     * @param {PerformanceEvent[]} events\r\n     * @param {?string} [correlationId]\r\n     */\r\n    emitEvents(events: PerformanceEvent[], correlationId: string): void {\r\n        this.logger.verbose(\"PerformanceClient: Emitting performance events\", correlationId);\r\n\r\n        this.callbacks.forEach((callback: PerformanceCallbackFunction, callbackId: string) => {\r\n            this.logger.trace(`PerformanceClient: Emitting event to callback ${callbackId}`, correlationId);\r\n            callback.apply(null, [events]);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Enforce truncation of integral fields in performance event.\r\n     * @param {PerformanceEvent} event performance event to update.\r\n     * @param {Set<string>} intFields integral fields.\r\n     */\r\n    private truncateIntegralFields(event: PerformanceEvent, intFields: ReadonlySet<string>): void {\r\n        intFields.forEach((key) => {\r\n            if (key in event && typeof event[key] === \"number\") {\r\n                event[key] = Math.floor(event[key]);\r\n            }\r\n        });\r\n    }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGG;AAyBH,IAAA,iBAAA,kBAAA,YAAA;AAgCI;;;;;;;;;;AAUG;IACH,SAAY,iBAAA,CAAA,QAAgB,EAAE,SAAiB,EAAE,MAAc,EAAE,WAAmB,EAAE,cAAsB,EAAE,oBAA0C,EAAA;AACpJ,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AAC/B,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;AACrC,QAAA,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;AACjD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACzB,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,EAAE,CAAC;AACvC,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,2BAA2B,GAAG,IAAI,GAAG,EAAE,CAAC;KAChD;AAUD;;;;;;;AAOG;;AAEH,IAAA,iBAAA,CAAA,SAAA,CAAA,2BAA2B,GAA3B,UAA4B,WAAmB,EAAE,aAAqB,EAAA;AAClE,QAAA,OAAO,EAA6B,CAAC;KACxC,CAAA;AAED;;;;;;;AAOG;;AAEH,IAAA,iBAAA,CAAA,SAAA,CAAA,4BAA4B,GAA5B,UAA6B,WAAmB,EAAE,aAAqB,EAAA;AACnE,QAAA,OAAO,EAA6B,CAAC;KACxC,CAAA;AAYD;;;AAGG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;AACI,QAAA,OAAO,SAAS,CAAC;KACpB,CAAA;AAED;;;;;;AAMG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,SAA4B,EAAE,aAAqB,EAAA;QAC/D,IAAM,aAAa,GAA8B,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAErG,IAAI,CAAC,aAAa,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iFAAkF,GAAA,aAAa,GAAmC,mCAAA,CAAC,CAAC;YACtJ,OAAO;AACV,SAAA;AAAM,aAAA,IAAI,aAAa,CAAC,IAAI,KAAK,SAAS,EAAE;YACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iEAAkE,GAAA,SAAS,GAAmC,mCAAA,CAAC,CAAC;YAClI,OAAO;AACV,SAAA;QAED,OAAO,aAAa,CAAC,IAAI,CAAC;KAC7B,CAAA;AAED;;;;;;;AAOG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,mBAAmB,GAAnB,UAAoB,YAAoB,EAAE,WAAmB,EAAA;QACzD,IAAI,YAAY,GAAG,CAAC,EAAE;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uEAAwE,GAAA,YAAc,CAAC,CAAC;AAC1G,YAAA,OAAO,CAAC,CAAC;AACZ,SAAA;QAED,IAAI,WAAW,GAAG,CAAC,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sEAAuE,GAAA,WAAa,CAAC,CAAC;AACxG,YAAA,OAAO,CAAC,CAAC;AACZ,SAAA;QAED,IAAI,WAAW,GAAG,YAAY,EAAE;AAC5B,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6FAA6F,CAAC,CAAC;AACjH,YAAA,OAAO,CAAC,CAAC;AACZ,SAAA;QAED,OAAO,WAAW,GAAC,YAAY,CAAC;KACnC,CAAA;AAED;;;;;;;;AAQG;IACH,iBAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,SAA4B,EAAE,aAAsB,EAAE,SAAkB,EAAE,iBAA2B,EAAA;QACrH,IAAI,CAAC,aAAa,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wEAAyE,GAAA,SAAS,GAAgC,gCAAA,CAAC,CAAC;YACtI,OAAO;AACV,SAAA;QAED,IAAI,SAAS,KAAK,CAAC,EAAE;;YAEjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oEAAkE,SAAS,GAAA,MAAA,GAAO,SAAW,CAAC,CAAC;AACpH,SAAA;aAAM,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oEAAqE,GAAA,SAAW,CAAC,CAAC;YACpG,OAAO;AACV,SAAA;AAED,QAAA,IAAM,gBAAgB,GAAqB,EAAC,SAAS,EAAA,SAAA,EAAE,SAAS,EAAA,SAAA,EAAE,iBAAiB,EAAA,iBAAA,EAAC,CAAC;;QAGrF,IAAM,oBAAoB,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACvE,QAAA,IAAI,oBAAoB,EAAE;AACtB,YAAA,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAAC;AACnE,SAAA;AAAM,aAAA;;YAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8DAA+D,GAAA,aAAa,GAAwB,wBAAA,CAAC,CAAC;AACxH,YAAA,IAAM,gBAAgB,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC5C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;AAC/D,SAAA;;AAED,QAAA,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;KAC1D,CAAA;AAED;;;;;;AAMG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,gBAAgB,GAAhB,UAAiB,WAA8B,EAAE,aAAsB,EAAA;QAAvE,IAqDC,KAAA,GAAA,IAAA,CAAA;;;QAnDG,IAAM,kBAAkB,GAAG,aAAa,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QAC9D,IAAI,CAAC,aAAa,EAAE;YAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oDAAqD,GAAA,WAAW,GAAc,cAAA,EAAE,kBAAkB,CAAC,CAAC;AACxH,SAAA;;QAGD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4DAA0D,WAAa,EAAE,kBAAkB,CAAC,CAAC;QAC/G,IAAM,sBAAsB,GAAG,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;QAClG,sBAAsB,CAAC,gBAAgB,EAAE,CAAC;AAE1C,QAAA,IAAM,eAAe,GAAqB;AACtC,YAAA,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,MAAM,EAAE,sBAAsB,CAAC,UAAU;YACzC,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACvB,YAAA,IAAI,EAAE,WAAW;AACjB,YAAA,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;AACvB,YAAA,aAAa,EAAE,kBAAkB;AACjC,YAAA,OAAO,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,oBAAoB,0CAAE,OAAO;AAC3C,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,oBAAoB,0CAAE,UAAU;SACpD,CAAC;;AAGF,QAAA,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,CAAC;;QAGhD,OAAO;YACH,cAAc,EAAE,UAAC,KAAiC,EAAA;gBAC9C,OAAO,KAAI,CAAC,cAAc,CAEnB,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,eAAe,GAEf,KAAK,CAAA,EAEZ,sBAAsB,CAAC,CAAC;aAC3B;AACD,YAAA,kBAAkB,EAAE,YAAA;gBAChB,OAAO,KAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;aAClE;YACD,eAAe,EAAE,UAAC,MAAoB,EAAA;gBAClC,OAAO,KAAI,CAAC,eAAe,CAAC,MAAM,EAAE,eAAe,CAAC,aAAa,CAAC,CAAC;aACtE;YACD,SAAS,EAAE,UAAC,QAAkB,EAAA;gBAC1B,OAAO,KAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,eAAe,CAAC,aAAa,CAAC,CAAC;aAClE;AACD,YAAA,WAAW,EAAE,sBAAsB;AACnC,YAAA,KAAK,EAAE,eAAe;SACzB,CAAC;KAEL,CAAA;AAED;;;;;;;;;AASG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,cAAc,GAAd,UAAe,KAAuB,EAAE,WAAqC,EAAA;QAA7E,IA0DC,KAAA,GAAA,IAAA,CAAA;;AAzDG,QAAA,IAAM,SAAS,GAAiC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QACpG,IAAI,CAAC,SAAS,EAAE;AACZ,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAAgD,GAAA,KAAK,CAAC,OAAS,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;AACxG,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;QAED,IAAM,MAAM,GAAG,KAAK,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,CAAC;AACnD,QAAA,IAAI,SAAS,GAAG;AACZ,YAAA,cAAc,EAAE,CAAC;AACjB,YAAA,eAAe,EAAE,CAAC;AAClB,YAAA,sBAAsB,EAAE,CAAC;SAC5B,CAAC;AACF,QAAA,IAAI,MAAM,EAAE;YACR,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACnD,YAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AAC9C,SAAA;AAAM,aAAA;YACH,CAAA,EAAA,GAAA,SAAS,CAAC,yBAAyB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA;AAC9D,SAAA;AAED,QAAA,WAAW,aAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,cAAc,EAAG,CAAA;QAC9B,IAAM,UAAU,GAAG,WAAW,KAAX,IAAA,IAAA,WAAW,uBAAX,WAAW,CAAE,gBAAgB,EAAE,CAAC;;QAEnD,IAAI,CAAC,UAAU,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sDAAsD,EAAE,SAAS,CAAC,aAAa,CAAC,CAAC;AACnG,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0DAAwD,KAAK,CAAC,IAAI,GAAA,IAAA,GAAK,UAAU,GAAK,KAAA,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;;QAG/H,IAAI,CAAC,MAAM,EAAE;AACT,YAAA,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,YAAY,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC9D,YAAA,OAAA,QAAA,CAAA,EAAA,EAAY,SAAS,CAAG,CAAA;AAC3B,SAAA;AAED,QAAA,IAAI,UAAU,GAA0B,QAAA,CAAA,QAAA,CAAA,EAAA,EAAA,SAAS,CAAK,EAAA,KAAK,CAAE,CAAC;QAC9D,IAAI,mBAAmB,GAAW,CAAC,CAAC;;AAEpC,QAAA,CAAA,EAAA,GAAA,UAAU,CAAC,yBAAyB,0CAAE,OAAO,CAAC,UAAA,cAAc,EAAA;AACxD,YAAA,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+CAAA,GAAgD,cAAc,CAAC,IAAI,GAAc,aAAA,GAAA,KAAK,CAAC,IAAM,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;AAC3I,YAAA,mBAAmB,EAAE,CAAC;AAC1B,SAAC,CAAE,CAAA;AACH,QAAA,UAAU,CAAC,yBAAyB,GAAG,SAAS,CAAC;AAEjD,QAAA,UAAU,yBACH,UAAU,CAAA,EAAA,EACb,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAClC,YAAY,EAAE,SAAS,CAAC,cAAc,EACtC,WAAW,EAAE,SAAS,CAAC,eAAe,EACtC,4BAA4B,EAAE,SAAS,CAAC,sBAAsB,EAC9D,MAAM,EAAE,sBAAsB,CAAC,SAAS,EACxC,mBAAmB,EAAA,mBAAA,GACtB,CAAC;QACF,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,UAAU,CAAC,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;AAEnD,QAAA,OAAO,UAAU,CAAC;KACrB,CAAA;AAED;;;;AAIG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,eAAe,GAAf,UAAgB,MAAoB,EAAE,aAAqB,EAAA;AACvD,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,IAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AAC5D,QAAA,IAAI,KAAK,EAAE;YACP,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,EAAA,QAAA,CAAA,QAAA,CAAA,EAAA,EAAM,KAAK,CAAA,EAAK,MAAM,CAAA,CAAE,CAAC;AACxE,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,aAAa,CAAC,CAAC;AAC9E,SAAA;KACJ,CAAA;AAED;;;;AAIG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,SAAS,GAAT,UAAU,QAAkB,EAAE,aAAqB,EAAA;AAC/C,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,IAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AAC5D,QAAA,IAAI,KAAK,EAAE;AACP,YAAA,KAAK,IAAM,OAAO,IAAI,QAAQ,EAAE;AAC5B,gBAAA,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;AAChC,oBAAA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACtB,iBAAA;gBACD,KAAK,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;AACvC,aAAA;AACJ,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,aAAa,CAAC,CAAC;AAC9E,SAAA;KACJ,CAAA;AAED;;;;;;;;AAQG;IACK,iBAAyB,CAAA,SAAA,CAAA,yBAAA,GAAjC,UAAkC,KAAuB,EAAA;AACrD,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACtE,QAAA,IAAI,SAAS,EAAE;AACX,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAkD,GAAA,KAAK,CAAC,IAAI,mBAAgB,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;YACrH,SAAS,CAAC,yBAAyB,GAAG,SAAS,CAAC,yBAAyB,IAAI,IAAI,GAAG,EAAE,CAAC;YACvF,SAAS,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,EAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;AAC/G,SAAA;AAAM,aAAA;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iDAAkD,GAAA,KAAK,CAAC,IAAI,aAAU,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;YAC/G,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,EAAA,QAAA,CAAA,EAAA,EAAO,KAAK,CAAA,CAAG,CAAC;AACrE,SAAA;KACJ,CAAA;IAEO,iBAAY,CAAA,SAAA,CAAA,YAAA,GAApB,UAAqB,aAAqB,EAAA;QACtC,IAAM,gCAAgC,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACnF,IAAI,CAAC,gCAAgC,EAAE;YACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wEAAyE,GAAA,aAAe,CAAC,CAAC;AAC/G,SAAA;QAED,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,sBAAsB,GAAG,CAAC,CAAC;QAC/B,gCAAgC,KAAA,IAAA,IAAhC,gCAAgC,KAAhC,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,gCAAgC,CAAE,OAAO,CAAC,UAAC,WAAW,EAAA;AAClD,YAAA,cAAc,IAAI,WAAW,CAAC,SAAS,CAAC;AACxC,YAAA,eAAe,EAAE,CAAC;AAClB,YAAA,sBAAsB,IAAI,WAAW,CAAC,iBAAiB,GAAG,CAAC,GAAG,CAAC,CAAC;AACpE,SAAC,CAAE,CAAA;QAEH,OAAO;AACH,YAAA,cAAc,EAAA,cAAA;AACd,YAAA,eAAe,EAAA,eAAA;AACf,YAAA,sBAAsB,EAAA,sBAAA;SACzB,CAAC;KACL,CAAA;AAED;;;;AAIG;IACH,iBAAmB,CAAA,SAAA,CAAA,mBAAA,GAAnB,UAAoB,aAAqB,EAAA;QACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uDAAuD,EAAE,aAAa,CAAC,CAAC;AAC1F,QAAA,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;KACpD,CAAA;AAED;;;;AAIG;IACK,iBAAY,CAAA,SAAA,CAAA,YAAA,GAApB,UAAqB,aAAqB,EAAA;AACtC,QAAA,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;QAExC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE,aAAa,CAAC,CAAC;AACnF,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAE7C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,aAAa,CAAC,CAAC;AACjF,QAAA,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;KAC1D,CAAA;AAED;;;;;AAKG;IACH,iBAAsB,CAAA,SAAA,CAAA,sBAAA,GAAtB,UAAuB,QAAqC,EAAA;AACxD,QAAA,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,8DAA+D,GAAA,UAAY,CAAC,CAAC;AAEjG,QAAA,OAAO,UAAU,CAAC;KACrB,CAAA;AAED;;;;;AAKG;IACH,iBAAyB,CAAA,SAAA,CAAA,yBAAA,GAAzB,UAA0B,UAAkB,EAAA;QACxC,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAEjD,QAAA,IAAI,MAAM,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0CAA2C,GAAA,UAAU,GAAW,WAAA,CAAC,CAAC;AACzF,SAAA;AAAM,aAAA;YACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0CAA2C,GAAA,UAAU,GAAe,eAAA,CAAC,CAAC;AAC7F,SAAA;AAED,QAAA,OAAO,MAAM,CAAC;KACjB,CAAA;AAED;;;;;AAKG;AACH,IAAA,iBAAA,CAAA,SAAA,CAAA,UAAU,GAAV,UAAW,MAA0B,EAAE,aAAqB,EAAA;QAA5D,IAOC,KAAA,GAAA,IAAA,CAAA;QANG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gDAAgD,EAAE,aAAa,CAAC,CAAC;QAErF,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,QAAqC,EAAE,UAAkB,EAAA;YAC7E,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAiD,UAAY,EAAE,aAAa,CAAC,CAAC;YAChG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACnC,SAAC,CAAC,CAAC;KACN,CAAA;AAED;;;;AAIG;AACK,IAAA,iBAAA,CAAA,SAAA,CAAA,sBAAsB,GAA9B,UAA+B,KAAuB,EAAE,SAA8B,EAAA;AAClF,QAAA,SAAS,CAAC,OAAO,CAAC,UAAC,GAAG,EAAA;YAClB,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE;AAChD,gBAAA,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACvC,aAAA;AACL,SAAC,CAAC,CAAC;KACN,CAAA;IACL,OAAC,iBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}