/**
 * Protocol modes supported by MSAL.
 */
export declare const ProtocolMode: {
    /**
     * Auth Code + PKCE with Entra ID (formerly AAD) specific optimizations and features
     */
    readonly AAD: "AAD";
    /**
     * Auth Code + PKCE without Entra ID specific optimizations and features. For use only with non-Microsoft owned authorities.
     * Support is limited for this mode.
     */
    readonly OIDC: "OIDC";
    /**
     * Encrypted Authorize Response (EAR) with Entra ID specific optimizations and features
     */
    readonly EAR: "EAR";
};
export type ProtocolMode = (typeof ProtocolMode)[keyof typeof ProtocolMode];
//# sourceMappingURL=ProtocolMode.d.ts.map