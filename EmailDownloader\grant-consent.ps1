# PowerShell script to grant admin consent for Microsoft Graph permissions
# Run this as an administrator

$TenantId = "2e765a21-6c42-4a30-8c32-c3eeae810ee1"
$ClientId = "64590f6c-0924-4dbd-b1b4-af5b6f3bb123"

Write-Host "Attempting to grant admin consent via PowerShell..." -ForegroundColor Yellow

# Install Microsoft Graph PowerShell if not already installed
if (!(Get-Module -ListAvailable -Name Microsoft.Graph)) {
    Write-Host "Installing Microsoft Graph PowerShell module..." -ForegroundColor Yellow
    Install-Module Microsoft.Graph -Scope CurrentUser -Force
}

try {
    # Connect with admin privileges
    Connect-MgGraph -TenantId $TenantId -Scopes "Application.ReadWrite.All", "AppRoleAssignment.ReadWrite.All"
    
    # Get the service principal for your app
    $servicePrincipal = Get-MgServicePrincipal -Filter "AppId eq '$ClientId'"
    
    if (!$servicePrincipal) {
        Write-Host "Service principal not found. Creating..." -ForegroundColor Yellow
        $servicePrincipal = New-MgServicePrincipal -AppId $ClientId
    }
    
    # Get Microsoft Graph service principal
    $graphServicePrincipal = Get-MgServicePrincipal -Filter "AppId eq '00000003-0000-0000-c000-000000000000'"
    
    # Required permissions
    $requiredPermissions = @("Mail.Read", "User.Read.All")
    
    foreach ($permission in $requiredPermissions) {
        $appRole = $graphServicePrincipal.AppRoles | Where-Object { $_.Value -eq $permission }
        
        if ($appRole) {
            try {
                # Grant the permission
                New-MgServicePrincipalAppRoleAssignment -ServicePrincipalId $servicePrincipal.Id -PrincipalId $servicePrincipal.Id -ResourceId $graphServicePrincipal.Id -AppRoleId $appRole.Id
                Write-Host "✅ Granted permission: $permission" -ForegroundColor Green
            } catch {
                if ($_.Exception.Message -like "*already exists*") {
                    Write-Host "✅ Permission already granted: $permission" -ForegroundColor Green
                } else {
                    Write-Host "❌ Failed to grant permission $permission`: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "❌ Permission not found: $permission" -ForegroundColor Red
        }
    }
    
    Write-Host "`n✅ Admin consent process completed!" -ForegroundColor Green
    Write-Host "Wait 5-10 minutes for changes to take effect, then test your application." -ForegroundColor Yellow
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may not have sufficient admin privileges." -ForegroundColor Yellow
} finally {
    Disconnect-MgGraph
}
