{"version": 3, "file": "CloudShell.mjs", "sources": ["../../../src/client/ManagedIdentitySources/CloudShell.ts"], "sourcesContent": [null], "names": ["ManagedIdentityErrorCodes.unableToCreateCloudShell"], "mappings": ";;;;;;;;AAAA;;;AAGG;AAqBH;;AAEG;AACG,MAAO,UAAW,SAAQ,yBAAyB,CAAA;IAGrD,WACI,CAAA,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAC9B,sBAA+B,EAC/B,WAAmB,EAAA;QAEnB,KAAK,CACD,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,CACzB,CAAC;AAEF,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;KAClC;AAEM,IAAA,OAAO,uBAAuB,GAAA;QACjC,MAAM,WAAW,GACb,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,YAAY,CAAC,CAAC;QAEtE,OAAO,CAAC,WAAW,CAAC,CAAC;KACxB;AAEM,IAAA,OAAO,SAAS,CACnB,MAAc,EACd,WAAwB,EACxB,aAA6B,EAC7B,cAA8B,EAC9B,sBAA+B,EAC/B,iBAAoC,EAAA;QAEpC,MAAM,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC,uBAAuB,EAAE,CAAC;;QAG3D,IAAI,CAAC,WAAW,EAAE;AACd,YAAA,MAAM,CAAC,IAAI,CACP,CAAA,mBAAA,EAAsB,0BAA0B,CAAC,WAAW,CAAA,8CAAA,EAAiD,uCAAuC,CAAC,YAAY,CAAA,qCAAA,CAAuC,CAC3M,CAAC;AACF,YAAA,OAAO,IAAI,CAAC;AACf,SAAA;AAED,QAAA,MAAM,oBAAoB,GACtB,UAAU,CAAC,gCAAgC,CACvC,uCAAuC,CAAC,YAAY,EACpD,WAAW,EACX,0BAA0B,CAAC,WAAW,EACtC,MAAM,CACT,CAAC;AAEN,QAAA,MAAM,CAAC,IAAI,CACP,CAAA,8DAAA,EAAiE,0BAA0B,CAAC,WAAW,CAAoC,iCAAA,EAAA,oBAAoB,cAAc,0BAA0B,CAAC,WAAW,CAAA,kBAAA,CAAoB,CAC1O,CAAC;AAEF,QAAA,IACI,iBAAiB,CAAC,MAAM,KAAK,qBAAqB,CAAC,eAAe,EACpE;AACE,YAAA,MAAM,0BAA0B,CAC5BA,wBAAkD,CACrD,CAAC;AACL,SAAA;AAED,QAAA,OAAO,IAAI,UAAU,CACjB,MAAM,EACN,WAAW,EACX,aAAa,EACb,cAAc,EACd,sBAAsB,EACtB,WAAW,CACd,CAAC;KACL;AAEM,IAAA,aAAa,CAAC,QAAgB,EAAA;AACjC,QAAA,MAAM,OAAO,GACT,IAAI,gCAAgC,CAChC,UAAU,CAAC,IAAI,EACf,IAAI,CAAC,WAAW,CACnB,CAAC;QAEN,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,GAAG,MAAM,CAAC;AAEtE,QAAA,OAAO,CAAC,cAAc,CAAC,8BAA8B,CAAC,QAAQ,CAAC;AAC3D,YAAA,QAAQ,CAAC;AAEb,QAAA,OAAO,OAAO,CAAC;KAClB;AACJ;;;;"}