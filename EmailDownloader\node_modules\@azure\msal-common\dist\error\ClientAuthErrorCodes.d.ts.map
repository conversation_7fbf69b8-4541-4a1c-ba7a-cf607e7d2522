{"version": 3, "file": "ClientAuthErrorCodes.d.ts", "sourceRoot": "", "sources": ["../../src/error/ClientAuthErrorCodes.ts"], "names": [], "mappings": "AAKA,eAAO,MAAM,uBAAuB,+BAA+B,CAAC;AACpE,eAAO,MAAM,oBAAoB,4BAA4B,CAAC;AAC9D,eAAO,MAAM,iBAAiB,wBAAwB,CAAC;AACvD,eAAO,MAAM,gBAAgB,wBAAwB,CAAC;AACtD,eAAO,MAAM,uBAAuB,+BAA+B,CAAC;AACpE,eAAO,MAAM,YAAY,kBAAkB,CAAC;AAC5C,eAAO,MAAM,iBAAiB,wBAAwB,CAAC;AACvD,eAAO,MAAM,mBAAmB,0BAA0B,CAAC;AAC3D,eAAO,MAAM,YAAY,kBAAkB,CAAC;AAC5C,eAAO,MAAM,aAAa,mBAAmB,CAAC;AAC9C,eAAO,MAAM,aAAa,oBAAoB,CAAC;AAC/C,eAAO,MAAM,aAAa,mBAAmB,CAAC;AAC9C,eAAO,MAAM,gBAAgB,wBAAwB,CAAC;AACtD,eAAO,MAAM,gBAAgB,uBAAuB,CAAC;AACrD,eAAO,MAAM,sBAAsB,6BAA6B,CAAC;AACjE,eAAO,MAAM,wBAAwB,+BAA+B,CAAC;AACrE,eAAO,MAAM,2BAA2B,kCAAkC,CAAC;AAC3E,eAAO,MAAM,mBAAmB,2BAA2B,CAAC;AAC5D,eAAO,MAAM,sBAAsB,8BAA8B,CAAC;AAClE,eAAO,MAAM,oBAAoB,2BAA2B,CAAC;AAC7D,eAAO,MAAM,kBAAkB,yBAAyB,CAAC;AACzD,eAAO,MAAM,0BAA0B,kCAAkC,CAAC;AAC1E,eAAO,MAAM,iBAAiB,wBAAwB,CAAC;AACvD,eAAO,MAAM,sBAAsB,8BAA8B,CAAC;AAClE,eAAO,MAAM,wBAAwB,iCAAiC,CAAC;AACvE,eAAO,MAAM,kBAAkB,yBAAyB,CAAC;AACzD,eAAO,MAAM,uBAAuB,8BAA8B,CAAC;AACnE,eAAO,MAAM,cAAc,qBAAqB,CAAC;AACjD,eAAO,MAAM,cAAc,qBAAqB,CAAC;AACjD,eAAO,MAAM,wBAAwB,+BAA+B,CAAC;AACrE,eAAO,MAAM,gBAAgB,sBAAsB,CAAC;AACpD,eAAO,MAAM,uBAAuB,8BAA8B,CAAC;AACnE,eAAO,MAAM,oBAAoB,2BAA2B,CAAC;AAC7D,eAAO,MAAM,kBAAkB,yBAAyB,CAAC;AACzD,eAAO,MAAM,kCAAkC,4CACF,CAAC;AAC9C,eAAO,MAAM,0CAA0C,oDACF,CAAC;AACtD,eAAO,MAAM,oBAAoB,4BAA4B,CAAC;AAC9D,eAAO,MAAM,8BAA8B,uCACH,CAAC;AACzC,eAAO,MAAM,YAAY,mBAAmB,CAAC;AAC7C,eAAO,MAAM,qBAAqB,4BAA4B,CAAC;AAC/D,eAAO,MAAM,YAAY,kBAAkB,CAAC;AAC5C,eAAO,MAAM,oBAAoB,4BAA4B,CAAC;AAC9D,eAAO,MAAM,oBAAoB,2BAA2B,CAAC;AAC7D,eAAO,MAAM,2BAA2B,oCAAoC,CAAC"}