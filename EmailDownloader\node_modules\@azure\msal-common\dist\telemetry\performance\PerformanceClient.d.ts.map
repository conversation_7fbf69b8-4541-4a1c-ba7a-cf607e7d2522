{"version": 3, "file": "PerformanceClient.d.ts", "sourceRoot": "", "sources": ["../../../src/telemetry/performance/PerformanceClient.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,oBAAoB,EAAE,MAAM,kCAAkC,CAAC;AACxE,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EACH,0BAA0B,EAC1B,kBAAkB,EAClB,2BAA2B,EAC3B,gBAAgB,EACnB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EACH,QAAQ,EAER,gBAAgB,EAChB,iBAAiB,EAEjB,YAAY,EACf,MAAM,oBAAoB,CAAC;AAE5B,MAAM,WAAW,aAAa;IAC1B,IAAI,EAAE,iBAAiB,CAAC;IACxB,IAAI,EAAE,MAAM,CAAC;CAChB;AAED,8BAAsB,iBAAkB,YAAW,kBAAkB;IACjE,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC;IAC5B,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC;IAC9B,SAAS,CAAC,cAAc,EAAE,MAAM,CAAC;IACjC,SAAS,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;IACrD,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC3B,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;IACzB,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,2BAA2B,CAAC,CAAC;IAE9D;;;;OAIG;IACH,SAAS,CAAC,qBAAqB,EAAE,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;IAE/D;;;;;OAKG;IACH,SAAS,CAAC,2BAA2B,EAAE,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IAElE;;;;;OAKG;IACH,SAAS,CAAC,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAElE;;;;;;;;;;OAUG;gBACS,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,oBAAoB,EAAE,oBAAoB;IAaxJ;;;;;OAKG;IACH,QAAQ,CAAC,UAAU,IAAI,MAAM;IAE7B;;;;;;;OAOG;IAEH,2BAA2B,CAAC,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,uBAAuB;IAIhG;;;;;;;OAOG;IAEH,4BAA4B,CAAC,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,uBAAuB;IAIjG;;;;;;;OAOG;IACH,QAAQ,CAAC,eAAe,CAAC,SAAS,EAAE,iBAAiB,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI;IAEpF;;;OAGG;IACH,YAAY,IAAI,WAAW,CAAC,MAAM,CAAC;IAInC;;;;;;OAMG;IACH,eAAe,CAAC,SAAS,EAAE,iBAAiB,EAAE,aAAa,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAcnF;;;;;;;OAOG;IACH,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,MAAM;IAmBtE;;;;;;;;OAQG;IACH,mBAAmB,CAAC,SAAS,EAAE,iBAAiB,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,OAAO,GAAG,IAAI;IA+BhI;;;;;;OAMG;IACH,gBAAgB,CAAC,WAAW,EAAE,iBAAiB,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,0BAA0B;IAuDpG;;;;;;;;;OASG;IACH,cAAc,CAAC,KAAK,EAAE,gBAAgB,EAAE,WAAW,CAAC,EAAE,uBAAuB,GAAG,gBAAgB,GAAG,IAAI;IA4DvG;;;;OAIG;IACH,eAAe,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,GAAI,IAAI;IAUnE;;;;OAIG;IACH,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IAe1D;;;;;;;;OAQG;IACH,OAAO,CAAC,yBAAyB;IAYjC,OAAO,CAAC,YAAY;IAsBpB;;;;OAIG;IACH,mBAAmB,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI;IAKhD;;;;OAIG;IACH,OAAO,CAAC,YAAY;IAUpB;;;;;OAKG;IACH,sBAAsB,CAAC,QAAQ,EAAE,2BAA2B,GAAG,MAAM;IAQrE;;;;;OAKG;IACH,yBAAyB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO;IAYtD;;;;;OAKG;IACH,UAAU,CAAC,MAAM,EAAE,gBAAgB,EAAE,EAAE,aAAa,EAAE,MAAM,GAAG,IAAI;IASnE;;;;OAIG;IACH,OAAO,CAAC,sBAAsB;CAOjC"}